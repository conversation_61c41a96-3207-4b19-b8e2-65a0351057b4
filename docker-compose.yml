services:
  app:
    build:
      dockerfile: ./docker/php/Dockerfile
      context: .
    environment:
      FORCE_HTTPS: off
      CRAFT_ENABLE_TEMPLATE_CACHING: "false"
    ports:
      - 80:8080
    volumes:
      - ./app:/app/app
      - ./config:/app/config
      - ./modules:/app/modules
      - ./migrations:/app/migrations
      - ./storage:/app/storage
      - ./templates:/app/templates
      - ./src/svg:/app/src/svg
      - ./web:/app/web
      - ./.env:/app/.env
      - ./bootstrap.php:/app/bootstrap.php
      - ./composer.json:/app/composer.json
      - ./composer.lock:/app/composer.lock
      - ./docker/php/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/php/https-dev.conf:/etc/nginx/https.conf
      - ./docker/php/rewrites.conf:/etc/nginx/rewrites.conf
    depends_on:
      mysql:
        condition: service_healthy

  console:
    build:
      dockerfile: ./docker/php/Dockerfile
      context: .
    entrypoint: ""
    command: ["php", "craft", "queue/listen"]
    volumes:
      - ./app:/app/app
      - ./config:/app/config
      - ./modules:/app/modules
      - ./migrations:/app/migrations
      - ./storage:/app/storage
      - ./templates:/app/templates
      - ./src/svg:/app/src/svg
      - ./web:/app/web
      - ./.env:/app/.env
      - ./bootstrap.php:/app/bootstrap.php
      - ./composer.json:/app/composer.json
      - ./composer.lock:/app/composer.lock
    depends_on:
      mysql:
        condition: service_healthy

  mysql:
    image: mysql:8.4
    environment:
      MYSQL_DATABASE: craft
      MYSQL_ROOT_PASSWORD: craft
    ports:
      - 3306:3306
    volumes:
      - ./mysql-data:/var/lib/mysql
      - ./docker/mysql/healthcheck.sh:/usr/local/bin/healthcheck.sh
    healthcheck:
      test: ["CMD", "bash", "/usr/local/bin/healthcheck.sh"]
      interval: 1s
      retries: 15

  frontend:
    build:
      dockerfile: ./docker/frontend/Dockerfile
      context: .
      target: dev
    volumes:
      - ./web/dist:/app/web/dist
      - ./src:/app/src
       - ./package.json:/app/package.json
       - ./pnpm-lock.yaml:/app/pnpm-lock.yaml
      - ./vite.config.js:/app/vite.config.js
      - ./vite.config.dev.js:/app/vite.config.dev.js
      - ./postcss.config.cjs:/app/postcss.config.cjs
      - ./templates:/app/templates
    ports:
      - 5173:5173
