services:
  app:
    image: vagrant.builtbybuffalo.com/buffalo/botanical-confetti/app:${IMAGE_TAG}
    environment:
      ENVIRONMENT: staging
      CRAFT_ENVIRONMENT: staging
      CRAFT_DB_USER: root
      CRAFT_DB_PASSWORD: ${MY<PERSON>QL_PASSWORD}
      CRAFT_DB_SERVER: mysql
      CRAFT_DB_DRIVER: mysql
      CRAFT_DB_DATABASE: craft
      CRAFT_DB_SCHEMA:
      CRAFT_DB_TABLE_PREFIX:
      CRAFT_DB_CHARSET: utf8mb4
      CRAFT_DB_COLLATION: utf8mb4_unicode_ci
      CRAFT_DB_PORT: 3306
      CRAFT_SECURITY_KEY: ${SECURITY_KEY}
      CP_TRIGGER: admin
      DEFAULT_SITE_URL: https://botanical.buffalo03.co.uk
      PRIMARY_SITE_URL: https://botanical.buffalo03.co.uk
      ALLOWED_SITE_HOSTS: botanical.buffalo03.co.uk
      VIRTUAL_HOST: ${VIRTUAL_HOST_NAME}
      VIRTUAL_PORT: '8080'
      LETSENCRYPT_HOST: ${VIRTUAL_HOST_NAME}
      LETSENCRYPT_EMAIL: <EMAIL>
    restart: unless-stopped
    volumes:
      - /opt/botanical/dist:/app/web/dist
      - /opt/botanical/uploads:/app/web/uploads

  console:
    image: vagrant.builtbybuffalo.com/buffalo/botanical-confetti/app:${IMAGE_TAG}
    environment:
      ENVIRONMENT: staging
      CRAFT_ENVIRONMENT: staging
      CRAFT_DB_USER: root
      CRAFT_DB_PASSWORD: ${MYSQL_PASSWORD}
      CRAFT_DB_SERVER: mysql
      CRAFT_DB_DRIVER: mysql
      CRAFT_DB_DATABASE: craft
      CRAFT_DB_SCHEMA:
      CRAFT_DB_TABLE_PREFIX:
      CRAFT_DB_CHARSET: utf8mb4
      CRAFT_DB_COLLATION: utf8mb4_unicode_ci
      CRAFT_DB_PORT: 3306
      CRAFT_SECURITY_KEY: ${SECURITY_KEY}
      CP_TRIGGER: admin
      DEFAULT_SITE_URL: https://botanical.buffalo03.co.uk
      PRIMARY_SITE_URL: https://botanical.buffalo03.co.uk
      ALLOWED_SITE_HOSTS: botanical.buffalo03.co.uk
    restart: unless-stopped
    entrypoint: ""
    command: ['php', 'craft', 'queue/listen']

  frontend:
    image: vagrant.builtbybuffalo.com/buffalo/botanical-confetti/frontend:${IMAGE_TAG}
    volumes:
      - /opt/botanical/dist:/app/web/dist

  redis:
    image: redis:5-alpine
    restart: unless-stopped

  mysql:
    image: mysql:8.4
    environment:
      MYSQL_DATABASE: craft
      MYSQL_ROOT_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - /opt/botanical/mysql:/var/lib/mysql
    restart: unless-stopped
