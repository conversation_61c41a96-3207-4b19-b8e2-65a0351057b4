{"name": "craftcms/craft", "description": "Craft CMS", "keywords": ["craft", "cms", "craftcms", "project"], "license": "0BSD", "homepage": "https://craftcms.com/", "type": "project", "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/cms/issues", "forum": "https://craftcms.stackexchange.com/", "source": "https://github.com/craftcms/cms", "docs": "https://craftcms.com/docs", "rss": "https://craftcms.com/changelog.rss"}, "minimum-stability": "dev", "prefer-stable": true, "require": {"craftcms/ckeditor": "^4.8", "craftcms/cms": "^5.0.0", "ether/seo": "^5.0", "nystudio107/craft-vite": "^5.0", "solspace/craft-freeform": "^5.10", "vlucas/phpdotenv": "^5.4.0"}, "require-dev": {"craftcms/generator": "^2.0.0", "yiisoft/yii2-shell": "^2.0.3"}, "config": {"allow-plugins": {"craftcms/plugin-installer": true, "yiisoft/yii2-composer": true}, "sort-packages": true, "optimize-autoloader": true, "platform": {"php": "8.2"}}, "scripts": {"post-create-project-cmd": ["@php -r \"file_exists('.env') || copy('.env.example.dev', '.env');\"", "@php -r \"unlink('composer.json');\"", "@php -r \"rename('composer.json.default', 'composer.json');\"", "@php craft install"]}}