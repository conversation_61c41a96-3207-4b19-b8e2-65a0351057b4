{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "ee210fe2c440a81b1beca720e5de8e58", "packages": [{"name": "bacon/bacon-qr-code", "version": "2.0.8", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "8674e51bb65af933a5ffaf1c308a660387c35c22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bacon/BaconQrCode/zipball/8674e51bb65af933a5ffaf1c308a660387c35c22", "reference": "8674e51bb65af933a5ffaf1c308a660387c35c22", "shasum": ""}, "require": {"dasprid/enum": "^1.0.3", "ext-iconv": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"phly/keep-a-changelog": "^2.1", "phpunit/phpunit": "^7 | ^8 | ^9", "spatie/phpunit-snapshot-assertions": "^4.2.9", "squizlabs/php_codesniffer": "^3.4"}, "suggest": {"ext-imagick": "to generate QR code images"}, "type": "library", "autoload": {"psr-4": {"BaconQrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "support": {"issues": "https://github.com/Bacon/BaconQrCode/issues", "source": "https://github.com/Bacon/BaconQrCode/tree/2.0.8"}, "time": "2022-12-07T17:46:57+00:00"}, {"name": "brick/math", "version": "0.12.3", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/866551da34e9a618e64a819ee1e01c20d8a588ba", "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^10.1", "vimeo/psalm": "6.8.8"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.12.3"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2025-02-28T13:11:00+00:00"}, {"name": "carbonphp/carbon-doctrine-types", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/dbal": "<4.0.0 || >=5.0.0"}, "require-dev": {"doctrine/dbal": "^4.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/3.2.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2024-02-09T16:56:22+00:00"}, {"name": "cebe/markdown", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/cebe/markdown.git", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/markdown/zipball/9bac5e971dd391e2802dca5400bbeacbaea9eb86", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86", "shasum": ""}, "require": {"lib-pcre": "*", "php": ">=5.4.0"}, "require-dev": {"cebe/indent": "*", "facebook/xhprof": "*@dev", "phpunit/phpunit": "4.1.*"}, "bin": ["bin/markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"cebe\\markdown\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Creator"}], "description": "A super fast, highly extensible markdown parser for PHP", "homepage": "https://github.com/cebe/markdown#readme", "keywords": ["extensible", "fast", "gfm", "markdown", "markdown-extra"], "support": {"issues": "https://github.com/cebe/markdown/issues", "source": "https://github.com/cebe/markdown"}, "time": "2018-03-26T11:24:36+00:00"}, {"name": "commerceguys/addressing", "version": "v2.2.4", "source": {"type": "git", "url": "https://github.com/commerceguys/addressing.git", "reference": "ea826dbe5b3fe76960073a2167d5cf996c811cda"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/commerceguys/addressing/zipball/ea826dbe5b3fe76960073a2167d5cf996c811cda", "reference": "ea826dbe5b3fe76960073a2167d5cf996c811cda", "shasum": ""}, "require": {"doctrine/collections": "^1.6 || ^2.0", "php": ">=8.0"}, "require-dev": {"ext-json": "*", "mikey179/vfsstream": "^1.6.11", "phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.7", "symfony/validator": "^5.4 || ^6.3 || ^7.0"}, "suggest": {"symfony/validator": "to validate addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"CommerceGuys\\Addressing\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}], "description": "Addressing library powered by CLDR and Google's address data.", "keywords": ["address", "internationalization", "localization", "postal"], "support": {"issues": "https://github.com/commerceguys/addressing/issues", "source": "https://github.com/commerceguys/addressing/tree/v2.2.4"}, "time": "2025-01-13T16:03:24+00:00"}, {"name": "composer/class-map-generator", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/composer/class-map-generator.git", "reference": "134b705ddb0025d397d8318a75825fe3c9d1da34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/class-map-generator/zipball/134b705ddb0025d397d8318a75825fe3c9d1da34", "reference": "134b705ddb0025d397d8318a75825fe3c9d1da34", "shasum": ""}, "require": {"composer/pcre": "^2.1 || ^3.1", "php": "^7.2 || ^8.0", "symfony/finder": "^4.4 || ^5.3 || ^6 || ^7"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-deprecation-rules": "^1 || ^2", "phpstan/phpstan-phpunit": "^1 || ^2", "phpstan/phpstan-strict-rules": "^1.1 || ^2", "phpunit/phpunit": "^8", "symfony/filesystem": "^5.4 || ^6"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\ClassMapGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Utilities to scan PHP code and generate class maps.", "keywords": ["classmap"], "support": {"issues": "https://github.com/composer/class-map-generator/issues", "source": "https://github.com/composer/class-map-generator/tree/1.6.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-03-24T13:50:44+00:00"}, {"name": "composer/pcre", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-11-12T16:29:46+00:00"}, {"name": "composer/semver", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-09-19T14:15:21+00:00"}, {"name": "craftcms/ckeditor", "version": "4.8.0", "source": {"type": "git", "url": "https://github.com/craftcms/ckeditor.git", "reference": "f96d936af6922e81e5c5a0a7b0df41a6cceaf88c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/ckeditor/zipball/f96d936af6922e81e5c5a0a7b0df41a6cceaf88c", "reference": "f96d936af6922e81e5c5a0a7b0df41a6cceaf88c", "shasum": ""}, "require": {"craftcms/cms": "^5.6.0", "craftcms/html-field": "^3.4.0", "nystudio107/craft-code-editor": ">=1.0.8 <=1.0.13 || ^1.0.16", "php": "^8.2"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main", "craftcms/rector": "dev-main", "vlucas/phpdotenv": "^5.5"}, "type": "craft-plugin", "extra": {"name": "CKEditor", "handle": "ckeditor", "documentationUrl": "https://github.com/craftcms/ckeditor/blob/master/README.md"}, "autoload": {"psr-4": {"craft\\ckeditor\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Edit rich text content in Craft CMS using CKEditor.", "keywords": ["CKEditor", "cms", "craftcms", "html", "yii2"], "support": {"docs": "https://github.com/craftcms/ckeditor/blob/master/README.md", "email": "<EMAIL>", "issues": "https://github.com/craftcms/ckeditor/issues?state=open", "rss": "https://github.com/craftcms/ckeditor/commits/master.atom", "source": "https://github.com/craftcms/ckeditor"}, "time": "2025-04-30T18:24:34+00:00"}, {"name": "craftcms/cms", "version": "5.7.6", "source": {"type": "git", "url": "https://github.com/craftcms/cms.git", "reference": "f425e47338b5baa00a0861d86401647b79ad6091"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/cms/zipball/f425e47338b5baa00a0861d86401647b79ad6091", "reference": "f425e47338b5baa00a0861d86401647b79ad6091", "shasum": ""}, "require": {"bacon/bacon-qr-code": "^2.0", "commerceguys/addressing": "^2.1.1", "composer/semver": "^3.3.2", "craftcms/plugin-installer": "~1.6.0", "craftcms/server-check": "~5.0.1", "creocoder/yii2-nested-sets": "~0.9.0", "elvanto/litemoji": "~4.3.0", "enshrined/svg-sanitize": "~0.19.0", "ext-bcmath": "*", "ext-curl": "*", "ext-dom": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-zip": "*", "guzzlehttp/guzzle": "^7.2.0", "illuminate/collections": "^v10.42.0", "league/uri": "^7.0", "mikehaertl/php-shellcommand": "^1.6.3", "moneyphp/money": "^4.0", "monolog/monolog": "^3.0", "php": "^8.2", "phpdocumentor/reflection-docblock": "^5.3", "pixelandtonic/imagine": "~*******", "pragmarx/google2fa": "^8.0", "pragmarx/recovery": "^0.2.1", "samdark/yii2-psr-log-target": "^1.1.3", "seld/cli-prompt": "^1.0.4", "symfony/css-selector": "^6.0|^7.0", "symfony/dom-crawler": "^6.0|^7.0", "symfony/filesystem": "^6.3", "symfony/http-client": "^6.0.3|^7.0", "symfony/property-access": "^7.0", "symfony/property-info": "^7.0", "symfony/serializer": "^6.4", "symfony/var-dumper": "^5.0|^6.0", "symfony/yaml": "^5.2.3|^6.0", "theiconic/name-parser": "^1.2", "twig/twig": "~3.15.0", "voku/stringy": "^6.4.0", "web-auth/webauthn-lib": "~4.9.0", "webonyx/graphql-php": "~14.11.10", "yiisoft/yii2": "~********", "yiisoft/yii2-debug": "~********", "yiisoft/yii2-queue": "~2.3.2", "yiisoft/yii2-symfonymailer": "^4.0.0"}, "provide": {"bower-asset/inputmask": "5.0.9", "bower-asset/jquery": "3.6.1", "bower-asset/punycode": "^1.4", "bower-asset/yii2-pjax": "~2.0.1", "yii2tech/ar-softdelete": "1.0.4"}, "require-dev": {"codeception/codeception": "^5.2.0", "codeception/lib-innerbrowser": "4.0.1", "codeception/module-asserts": "^3.0.0", "codeception/module-datafactory": "^3.0.0", "codeception/module-phpbrowser": "^3.0.0", "codeception/module-rest": "^3.3.2", "codeception/module-yii2": "^1.1.9", "craftcms/ecs": "dev-main", "fakerphp/faker": "^1.19.0", "league/factory-muffin": "^3.3.0", "phpstan/phpstan": "^1.10.56", "rector/rector": "^1.2", "vlucas/phpdotenv": "^5.4.1", "yiisoft/yii2-redis": "^2.0"}, "suggest": {"ext-exif": "Adds support for parsing image EXIF data.", "ext-iconv": "Adds support for more character encodings than PHP’s built-in mb_convert_encoding() function, which Craft will take advantage of when converting strings to UTF-8.", "ext-imagick": "Adds support for more image processing formats and options."}, "type": "library", "autoload": {"psr-4": {"craft\\": "src/", "yii2tech\\ar\\softdelete\\": "lib/ar-softdelete/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["proprietary"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Craft CMS", "homepage": "https://craftcms.com", "keywords": ["cms", "craftcms", "yii2"], "support": {"docs": "https://craftcms.com/docs/5.x/", "email": "<EMAIL>", "forum": "https://craftcms.stackexchange.com/", "issues": "https://github.com/craftcms/cms/issues?state=open", "rss": "https://github.com/craftcms/cms/releases.atom", "source": "https://github.com/craftcms/cms"}, "time": "2025-05-14T23:51:36+00:00"}, {"name": "craftcms/html-field", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/craftcms/html-field.git", "reference": "3f23569c94d64e9054e3402447e03bd3c4c7b181"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/html-field/zipball/3f23569c94d64e9054e3402447e03bd3c4c7b181", "reference": "3f23569c94d64e9054e3402447e03bd3c4c7b181", "shasum": ""}, "require": {"craftcms/cms": "^5.5.0", "league/html-to-markdown": "^5.1", "php": "^8.2", "symfony/css-selector": "^6.0|^7.0", "symfony/dom-crawler": "^6.0|^7.0"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main", "craftcms/rector": "dev-main"}, "type": "library", "autoload": {"psr-4": {"craft\\htmlfield\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Base class for Craft CMS field types with HTML values.", "support": {"docs": "https://github.com/craftcms/html-field/blob/main/README.md", "email": "<EMAIL>", "issues": "https://github.com/craftcms/html-field/issues?state=open", "rss": "https://github.com/craftcms/html-field/commits/main.atom", "source": "https://github.com/craftcms/html-field"}, "time": "2025-04-30T16:54:07+00:00"}, {"name": "craftcms/plugin-installer", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/craftcms/plugin-installer.git", "reference": "bd1650e8da6d5ca7a8527068d3e51c34bc7b6b4f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/plugin-installer/zipball/bd1650e8da6d5ca7a8527068d3e51c34bc7b6b4f", "reference": "bd1650e8da6d5ca7a8527068d3e51c34bc7b6b4f", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.4"}, "require-dev": {"composer/composer": "^1.0 || ^2.0"}, "type": "composer-plugin", "extra": {"class": "craft\\composer\\Plugin"}, "autoload": {"psr-4": {"craft\\composer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Craft CMS Plugin Installer", "homepage": "https://craftcms.com/", "keywords": ["cms", "composer", "craftcms", "installer", "plugin"], "support": {"docs": "https://craftcms.com/docs", "email": "<EMAIL>", "forum": "https://craftcms.stackexchange.com/", "issues": "https://github.com/craftcms/cms/issues?state=open", "rss": "https://craftcms.com/changelog.rss", "source": "https://github.com/craftcms/cms"}, "time": "2023-02-22T13:17:00+00:00"}, {"name": "craftcms/server-check", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/craftcms/server-check.git", "reference": "08082638f8caff8ab86a223898e8ea167b3f5879"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/server-check/zipball/08082638f8caff8ab86a223898e8ea167b3f5879", "reference": "08082638f8caff8ab86a223898e8ea167b3f5879", "shasum": ""}, "type": "library", "autoload": {"classmap": ["server/requirements"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Craft CMS Server Check", "homepage": "https://craftcms.com/", "keywords": ["cms", "craftcms", "requirements", "yii2"], "support": {"docs": "https://github.com/craftcms/docs", "email": "<EMAIL>", "forum": "https://craftcms.stackexchange.com/", "issues": "https://github.com/craftcms/server-check/issues?state=open", "rss": "https://github.com/craftcms/server-check/releases.atom", "source": "https://github.com/craftcms/server-check"}, "time": "2025-02-11T20:26:29+00:00"}, {"name": "creocoder/yii2-nested-sets", "version": "0.9.0", "source": {"type": "git", "url": "https://github.com/creocoder/yii2-nested-sets.git", "reference": "cb8635a459b6246e5a144f096b992dcc30cf9954"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/creocoder/yii2-nested-sets/zipball/cb8635a459b6246e5a144f096b992dcc30cf9954", "reference": "cb8635a459b6246e5a144f096b992dcc30cf9954", "shasum": ""}, "require": {"yiisoft/yii2": "*"}, "type": "yii2-extension", "autoload": {"psr-4": {"creocoder\\nestedsets\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The nested sets behavior for the Yii framework", "keywords": ["nested sets", "yii2"], "support": {"issues": "https://github.com/creocoder/yii2-nested-sets/issues", "source": "https://github.com/creocoder/yii2-nested-sets/tree/master"}, "time": "2015-01-27T10:53:51+00:00"}, {"name": "dasprid/enum", "version": "1.0.6", "source": {"type": "git", "url": "https://github.com/DASPRiD/Enum.git", "reference": "8dfd07c6d2cf31c8da90c53b83c026c7696dda90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DASPRiD/Enum/zipball/8dfd07c6d2cf31c8da90c53b83c026c7696dda90", "reference": "8dfd07c6d2cf31c8da90c53b83c026c7696dda90", "shasum": ""}, "require": {"php": ">=7.1 <9.0"}, "require-dev": {"phpunit/phpunit": "^7 || ^8 || ^9 || ^10 || ^11", "squizlabs/php_codesniffer": "*"}, "type": "library", "autoload": {"psr-4": {"DASPRiD\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "PHP 7.1 enum implementation", "keywords": ["enum", "map"], "support": {"issues": "https://github.com/DASPRiD/Enum/issues", "source": "https://github.com/DASPRiD/Enum/tree/1.0.6"}, "time": "2024-08-09T14:30:48+00:00"}, {"name": "defuse/php-encryption", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/defuse/php-encryption.git", "reference": "f53396c2d34225064647a05ca76c1da9d99e5828"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/defuse/php-encryption/zipball/f53396c2d34225064647a05ca76c1da9d99e5828", "reference": "f53396c2d34225064647a05ca76c1da9d99e5828", "shasum": ""}, "require": {"ext-openssl": "*", "paragonie/random_compat": ">= 2", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^5|^6|^7|^8|^9|^10", "yoast/phpunit-polyfills": "^2.0.0"}, "bin": ["bin/generate-defuse-key"], "type": "library", "autoload": {"psr-4": {"Defuse\\Crypto\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://defuse.ca/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "Secure PHP Encryption Library", "keywords": ["aes", "authenticated encryption", "cipher", "crypto", "cryptography", "encrypt", "encryption", "openssl", "security", "symmetric key cryptography"], "support": {"issues": "https://github.com/defuse/php-encryption/issues", "source": "https://github.com/defuse/php-encryption/tree/v2.4.0"}, "time": "2023-06-19T06:10:36+00:00"}, {"name": "doctrine/annotations", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "901c2ee5d26eb64ff43c47976e114bf00843acf7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/901c2ee5d26eb64ff43c47976e114bf00843acf7", "reference": "901c2ee5d26eb64ff43c47976e114bf00843acf7", "shasum": ""}, "require": {"doctrine/lexer": "^2 || ^3", "ext-tokenizer": "*", "php": "^7.2 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^2.0", "doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.10.28", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^5.4 || ^6.4 || ^7", "vimeo/psalm": "^4.30 || ^5.14"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/2.0.2"}, "time": "2024-09-05T10:17:24+00:00"}, {"name": "doctrine/collections", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2eb07e5953eed811ce1b309a7478a3b236f2273d", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d", "shasum": ""}, "require": {"doctrine/deprecations": "^1", "php": "^8.1", "symfony/polyfill-php84": "^1.30"}, "require-dev": {"doctrine/coding-standard": "^12", "ext-json": "*", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^10.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.3.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcollections", "type": "tidelift"}], "time": "2025-03-22T10:17:19+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/lexer", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:56:58+00:00"}, {"name": "dompdf/dompdf", "version": "v2.0.8", "source": {"type": "git", "url": "https://github.com/dompdf/dompdf.git", "reference": "c20247574601700e1f7c8dab39310fca1964dc52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/dompdf/zipball/c20247574601700e1f7c8dab39310fca1964dc52", "reference": "c20247574601700e1f7c8dab39310fca1964dc52", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "masterminds/html5": "^2.0", "phenx/php-font-lib": ">=0.5.4 <1.0.0", "phenx/php-svg-lib": ">=0.5.2 <1.0.0", "php": "^7.1 || ^8.0"}, "require-dev": {"ext-json": "*", "ext-zip": "*", "mockery/mockery": "^1.3", "phpunit/phpunit": "^7.5 || ^8 || ^9", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-gd": "Needed to process images", "ext-gmagick": "Improves image processing performance", "ext-imagick": "Improves image processing performance", "ext-zlib": "Needed for pdf stream compression"}, "type": "library", "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "The Dompdf Community", "homepage": "https://github.com/dompdf/dompdf/blob/master/AUTHORS.md"}], "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "support": {"issues": "https://github.com/dompdf/dompdf/issues", "source": "https://github.com/dompdf/dompdf/tree/v2.0.8"}, "time": "2024-04-29T13:06:17+00:00"}, {"name": "egulias/email-validator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.4"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2025-03-06T22:45:56+00:00"}, {"name": "elvanto/litemoji", "version": "4.3.0", "source": {"type": "git", "url": "https://github.com/elvanto/litemoji.git", "reference": "f13cf10686f7110a3b17d09de03050d0708840b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elvanto/litemoji/zipball/f13cf10686f7110a3b17d09de03050d0708840b8", "reference": "f13cf10686f7110a3b17d09de03050d0708840b8", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=7.3"}, "require-dev": {"milesj/emojibase": "7.0.*", "phpunit/phpunit": "^9.0"}, "type": "library", "autoload": {"psr-4": {"LitEmoji\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library simplifying the conversion of unicode, HTML and shortcode emoji.", "keywords": ["emoji", "php-emoji"], "support": {"issues": "https://github.com/elvanto/litemoji/issues", "source": "https://github.com/elvanto/litemoji/tree/4.3.0"}, "time": "2022-10-28T02:32:19+00:00"}, {"name": "enshrined/svg-sanitize", "version": "0.19.0", "source": {"type": "git", "url": "https://github.com/darylldoyle/svg-sanitizer.git", "reference": "e95cd17be68e45f523cbfb0fe50cdd891b0cf20e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/darylldoyle/svg-sanitizer/zipball/e95cd17be68e45f523cbfb0fe50cdd891b0cf20e", "reference": "e95cd17be68e45f523cbfb0fe50cdd891b0cf20e", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.5 || ^8.5"}, "type": "library", "autoload": {"psr-4": {"enshrined\\svgSanitize\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An SVG sanitizer for PHP", "support": {"issues": "https://github.com/darylldoyle/svg-sanitizer/issues", "source": "https://github.com/darylldoyle/svg-sanitizer/tree/0.19.0"}, "time": "2024-06-18T10:27:15+00:00"}, {"name": "ether/seo", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/ethercreative/seo.git", "reference": "aa23f778c5d54e8ff387ba36f624cc7b4f23b3d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ethercreative/seo/zipball/aa23f778c5d54e8ff387ba36f624cc7b4f23b3d7", "reference": "aa23f778c5d54e8ff387ba36f624cc7b4f23b3d7", "shasum": ""}, "require": {"craftcms/cms": "^5.0", "php": "^8.2"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/rector": "dev-main", "phpstan/phpstan": "^1.6"}, "type": "craft-plugin", "extra": {"name": "SEO", "class": "ether\\seo\\Seo", "handle": "seo", "developer": "<PERSON>ther <PERSON>", "developerUrl": "https://ethercreative.co.uk"}, "autoload": {"psr-4": {"ether\\seo\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "SEO utilities including a unique field type, sitemap, & redirect manager", "support": {"docs": "https://github.com/ethercreative/seo/blob/v3/README.md", "issues": "https://github.com/ethercreative/seo", "source": "https://github.com/ethercreative/seo/tree/5.0.0"}, "funding": [{"url": "https://github.com/tam", "type": "github"}], "time": "2024-10-09T10:52:49+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.18.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "cb56001e54359df7ae76dc522d08845dc741621b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/cb56001e54359df7ae76dc522d08845dc741621b", "reference": "cb56001e54359df7ae76dc522d08845dc741621b", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.18.0"}, "time": "2024-11-01T03:51:45+00:00"}, {"name": "fakerphp/faker", "version": "v1.24.1", "source": {"type": "git", "url": "https://github.com/FakerPHP/Faker.git", "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FakerPHP/Faker/zipball/e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5", "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "conflict": {"fzaninotto/faker": "*"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "ext-intl": "*", "phpunit/phpunit": "^9.5.26", "symfony/phpunit-bridge": "^5.4.16"}, "suggest": {"doctrine/orm": "Required to use Faker\\ORM\\Doctrine", "ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality."}, "type": "library", "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.24.1"}, "time": "2024-11-21T13:46:39+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/3ba905c11371512af9d9bdd27d99b782216b6945", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2024-07-20T21:45:45+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2025-03-27T13:27:01+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "hashids/hashids", "version": "5.0.2", "source": {"type": "git", "url": "https://github.com/vinkla/hashids.git", "reference": "197171016b77ddf14e259e186559152eb3f8cf33"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vinkla/hashids/zipball/197171016b77ddf14e259e186559152eb3f8cf33", "reference": "197171016b77ddf14e259e186559152eb3f8cf33", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "suggest": {"ext-bcmath": "Required to use BC Math arbitrary precision mathematics (*).", "ext-gmp": "Required to use GNU multiple precision mathematics (*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"Hashids\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Generate short, unique, non-sequential ids (like YouTube and Bitly) from numbers", "homepage": "https://hashids.org/php", "keywords": ["bitly", "decode", "encode", "hash", "hashid", "hashids", "ids", "obfuscate", "youtube"], "support": {"issues": "https://github.com/vinkla/hashids/issues", "source": "https://github.com/vinkla/hashids/tree/5.0.2"}, "time": "2023-02-23T15:00:54+00:00"}, {"name": "illuminate/collections", "version": "v10.48.28", "source": {"type": "git", "url": "https://github.com/illuminate/collections.git", "reference": "48de3d6bc6aa779112ddcb608a3a96fc975d89d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/collections/zipball/48de3d6bc6aa779112ddcb608a3a96fc975d89d8", "reference": "48de3d6bc6aa779112ddcb608a3a96fc975d89d8", "shasum": ""}, "require": {"illuminate/conditionable": "^10.0", "illuminate/contracts": "^10.0", "illuminate/macroable": "^10.0", "php": "^8.1"}, "suggest": {"symfony/var-dumper": "Required to use the dump method (^6.2)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "autoload": {"files": ["helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Collections package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2024-11-21T14:02:44+00:00"}, {"name": "illuminate/conditionable", "version": "v10.48.28", "source": {"type": "git", "url": "https://github.com/illuminate/conditionable.git", "reference": "3ee34ac306fafc2a6f19cd7cd68c9af389e432a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/conditionable/zipball/3ee34ac306fafc2a6f19cd7cd68c9af389e432a5", "reference": "3ee34ac306fafc2a6f19cd7cd68c9af389e432a5", "shasum": ""}, "require": {"php": "^8.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Conditionable package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2024-11-21T14:02:44+00:00"}, {"name": "illuminate/contracts", "version": "v10.48.28", "source": {"type": "git", "url": "https://github.com/illuminate/contracts.git", "reference": "f90663a69f926105a70b78060a31f3c64e2d1c74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/contracts/zipball/f90663a69f926105a70b78060a31f3c64e2d1c74", "reference": "f90663a69f926105a70b78060a31f3c64e2d1c74", "shasum": ""}, "require": {"php": "^8.1", "psr/container": "^1.1.1|^2.0.1", "psr/simple-cache": "^1.0|^2.0|^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2024-11-21T14:02:44+00:00"}, {"name": "illuminate/macroable", "version": "v10.48.28", "source": {"type": "git", "url": "https://github.com/illuminate/macroable.git", "reference": "dff667a46ac37b634dcf68909d9d41e94dc97c27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/macroable/zipball/dff667a46ac37b634dcf68909d9d41e94dc97c27", "reference": "dff667a46ac37b634dcf68909d9d41e94dc97c27", "shasum": ""}, "require": {"php": "^8.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Macroable package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2023-06-05T12:46:42+00:00"}, {"name": "lcobucci/clock", "version": "3.3.1", "source": {"type": "git", "url": "https://github.com/lcobucci/clock.git", "reference": "db3713a61addfffd615b79bf0bc22f0ccc61b86b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/clock/zipball/db3713a61addfffd615b79bf0bc22f0ccc61b86b", "reference": "db3713a61addfffd615b79bf0bc22f0ccc61b86b", "shasum": ""}, "require": {"php": "~8.2.0 || ~8.3.0 || ~8.4.0", "psr/clock": "^1.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"infection/infection": "^0.29", "lcobucci/coding-standard": "^11.1.0", "phpstan/extension-installer": "^1.3.1", "phpstan/phpstan": "^1.10.25", "phpstan/phpstan-deprecation-rules": "^1.1.3", "phpstan/phpstan-phpunit": "^1.3.13", "phpstan/phpstan-strict-rules": "^1.5.1", "phpunit/phpunit": "^11.3.6"}, "type": "library", "autoload": {"psr-4": {"Lcobucci\\Clock\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Yet another clock abstraction", "support": {"issues": "https://github.com/lcobucci/clock/issues", "source": "https://github.com/lcobucci/clock/tree/3.3.1"}, "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "time": "2024-09-24T20:45:14+00:00"}, {"name": "league/html-to-markdown", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/thephpleague/html-to-markdown.git", "reference": "0b4066eede55c48f38bcee4fb8f0aa85654390fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/html-to-markdown/zipball/0b4066eede55c48f38bcee4fb8f0aa85654390fd", "reference": "0b4066eede55c48f38bcee4fb8f0aa85654390fd", "shasum": ""}, "require": {"ext-dom": "*", "ext-xml": "*", "php": "^7.2.5 || ^8.0"}, "require-dev": {"mikehaertl/php-shellcommand": "^1.1.0", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^8.5 || ^9.2", "scrutinizer/ocular": "^1.6", "unleashedtech/php-coding-standard": "^2.7 || ^3.0", "vimeo/psalm": "^4.22 || ^5.0"}, "bin": ["bin/html-to-markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}, "autoload": {"psr-4": {"League\\HTMLToMarkdown\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://modernnerd.net", "role": "Original Author"}], "description": "An HTML-to-markdown conversion helper for PHP", "homepage": "https://github.com/thephpleague/html-to-markdown", "keywords": ["html", "markdown"], "support": {"issues": "https://github.com/thephpleague/html-to-markdown/issues", "source": "https://github.com/thephpleague/html-to-markdown/tree/5.1.1"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/html-to-markdown", "type": "tidelift"}], "time": "2023-07-12T21:21:09+00:00"}, {"name": "league/uri", "version": "7.5.1", "source": {"type": "git", "url": "https://github.com/thephpleague/uri.git", "reference": "81fb5145d2644324614cc532b28efd0215bda430"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri/zipball/81fb5145d2644324614cc532b28efd0215bda430", "reference": "81fb5145d2644324614cc532b28efd0215bda430", "shasum": ""}, "require": {"league/uri-interfaces": "^7.5", "php": "^8.1"}, "conflict": {"league/uri-schemes": "^1.0"}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-fileinfo": "to create Data URI from file contennts", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "jeremykendall/php-domain-parser": "to resolve Public Suffix and Top Level Domain", "league/uri-components": "Needed to easily manipulate URI objects components", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "https://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "middleware", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "rfc6570", "uri", "uri-template", "url", "ws"], "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.5.1"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2024-12-08T08:40:02+00:00"}, {"name": "league/uri-interfaces", "version": "7.5.0", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-interfaces.git", "reference": "08cfc6c4f3d811584fb09c37e2849e6a7f9b0742"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/08cfc6c4f3d811584fb09c37e2849e6a7f9b0742", "reference": "08cfc6c4f3d811584fb09c37e2849e6a7f9b0742", "shasum": ""}, "require": {"ext-filter": "*", "php": "^8.1", "psr/http-factory": "^1", "psr/http-message": "^1.1 || ^2.0"}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "Common interfaces and classes for URI representation and interaction", "homepage": "https://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "rfc6570", "uri", "url", "ws"], "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/7.5.0"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2024-12-08T08:18:47+00:00"}, {"name": "maennchen/zipstream-php", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "aeadcf5c412332eb426c0f9b4485f6accba2a99f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/aeadcf5c412332eb426c0f9b4485f6accba2a99f", "reference": "aeadcf5c412332eb426c0f9b4485f6accba2a99f", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-zlib": "*", "php-64bit": "^8.2"}, "require-dev": {"brianium/paratest": "^7.7", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.16", "guzzlehttp/guzzle": "^7.5", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^11.0", "vimeo/psalm": "^6.0"}, "suggest": {"guzzlehttp/psr7": "^2.4", "psr/http-message": "^2.0"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/3.1.2"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}], "time": "2025-01-27T12:07:53+00:00"}, {"name": "markbaker/complex", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.2"}, "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "728434227fe21be27ff6d86621a1b13107a2562c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.1"}, "time": "2022-12-02T22:17:43+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "time": "2024-03-31T07:05:07+00:00"}, {"name": "mikehaertl/php-shellcommand", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/mikehaertl/php-shellcommand.git", "reference": "e79ea528be155ffdec6f3bf1a4a46307bb49e545"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mikehaertl/php-shellcommand/zipball/e79ea528be155ffdec6f3bf1a4a46307bb49e545", "reference": "e79ea528be155ffdec6f3bf1a4a46307bb49e545", "shasum": ""}, "require": {"php": ">= 5.3.0"}, "require-dev": {"phpunit/phpunit": ">4.0 <=9.4"}, "type": "library", "autoload": {"psr-4": {"mikehaertl\\shellcommand\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "An object oriented interface to shell commands", "keywords": ["shell"], "support": {"issues": "https://github.com/mikehaertl/php-shellcommand/issues", "source": "https://github.com/mikehaertl/php-shellcommand/tree/1.7.0"}, "time": "2023-04-19T08:25:22+00:00"}, {"name": "moneyphp/money", "version": "v4.7.0", "source": {"type": "git", "url": "https://github.com/moneyphp/money.git", "reference": "af048f0206d3b39b8fad9de6a230cedf765365fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/moneyphp/money/zipball/af048f0206d3b39b8fad9de6a230cedf765365fa", "reference": "af048f0206d3b39b8fad9de6a230cedf765365fa", "shasum": ""}, "require": {"ext-bcmath": "*", "ext-filter": "*", "ext-json": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"cache/taggable-cache": "^1.1.0", "doctrine/coding-standard": "^12.0", "doctrine/instantiator": "^1.5.0 || ^2.0", "ext-gmp": "*", "ext-intl": "*", "florianv/exchanger": "^2.8.1", "florianv/swap": "^4.3.0", "moneyphp/crypto-currencies": "^1.1.0", "moneyphp/iso-currencies": "^3.4", "php-http/message": "^1.16.0", "php-http/mock-client": "^1.6.0", "phpbench/phpbench": "^1.2.5", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1.9", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.5.9", "psr/cache": "^1.0.1 || ^2.0 || ^3.0", "ticketswap/phpstan-error-formatter": "^1.1"}, "suggest": {"ext-gmp": "Calculate without integer limits", "ext-intl": "Format Money objects with intl", "florianv/exchanger": "Exchange rates library for PHP", "florianv/swap": "Exchange rates library for PHP", "psr/cache-implementation": "Used for Currency caching"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Money\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://verraes.net"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP implementation of Fowler's Money pattern", "homepage": "http://moneyphp.org", "keywords": ["Value Object", "money", "vo"], "support": {"issues": "https://github.com/moneyphp/money/issues", "source": "https://github.com/moneyphp/money/tree/v4.7.0"}, "time": "2025-04-03T08:26:36+00:00"}, {"name": "monolog/monolog", "version": "3.9.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/10d85740180ecba7896c87e06a166e0c95a0e3b6", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.8", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^10.5.17 || ^11.0.7", "predis/predis": "^1.1 || ^2", "rollbar/rollbar": "^4.0", "ruflin/elastica": "^7 || ^8", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.9.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2025-03-24T10:02:05+00:00"}, {"name": "nesbot/carbon", "version": "3.9.1", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "ced71f79398ece168e24f7f7710462f462310d4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/ced71f79398ece168e24f7f7710462f462310d4d", "reference": "ced71f79398ece168e24f7f7710462f462310d4d", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "<100.0", "ext-json": "*", "php": "^8.1", "psr/clock": "^1.0", "symfony/clock": "^6.3 || ^7.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^4.4.18 || ^5.2.1|| ^6.0 || ^7.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^3.6.3 || ^4.0", "doctrine/orm": "^2.15.2 || ^3.0", "friendsofphp/php-cs-fixer": "^3.57.2", "kylekatarnls/multi-tester": "^2.5.3", "ondrejmirtes/better-reflection": "^********", "phpmd/phpmd": "^2.15.0", "phpstan/extension-installer": "^1.3.1", "phpstan/phpstan": "^1.11.2", "phpunit/phpunit": "^10.5.20", "squizlabs/php_codesniffer": "^3.9.0"}, "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2025-05-01T19:51:51+00:00"}, {"name": "nystudio107/craft-code-editor", "version": "1.0.23", "source": {"type": "git", "url": "https://github.com/nystudio107/craft-code-editor.git", "reference": "0a206d73efa8f8d3bd7294ae68b3d70c696b19c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-code-editor/zipball/0a206d73efa8f8d3bd7294ae68b3d70c696b19c5", "reference": "0a206d73efa8f8d3bd7294ae68b3d70c696b19c5", "shasum": ""}, "require": {"craftcms/cms": "^3.0.0 || ^4.0.0 || ^5.0.0", "phpdocumentor/reflection-docblock": "^5.0.0"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main", "craftcms/rector": "dev-main"}, "type": "yii2-extension", "extra": {"bootstrap": "nystudio107\\codeeditor\\CodeEditor"}, "autoload": {"psr-4": {"nystudio107\\codeeditor\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com"}], "description": "Provides a code editor field with Twig & Craft API autocomplete", "keywords": ["Craft", "Monaco", "cms", "code", "craftcms", "css", "editor", "javascript", "markdown", "twig"], "support": {"docs": "https://github.com/nystudio107/craft-code-editor/blob/v1/README.md", "issues": "https://github.com/nystudio107/craft-code-editor/issues", "source": "https://github.com/nystudio107/craft-code-editor"}, "funding": [{"url": "https://github.com/khalwat", "type": "github"}], "time": "2025-05-17T20:38:20+00:00"}, {"name": "nystudio107/craft-plugin-vite", "version": "5.0.2", "source": {"type": "git", "url": "https://github.com/nystudio107/craft-plugin-vite.git", "reference": "023ea9013f03ac8b15c9b800c66943ce4d02cf48"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-plugin-vite/zipball/023ea9013f03ac8b15c9b800c66943ce4d02cf48", "reference": "023ea9013f03ac8b15c9b800c66943ce4d02cf48", "shasum": ""}, "require": {"craftcms/cms": "^5.0.0"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main", "craftcms/rector": "dev-main"}, "type": "library", "autoload": {"psr-4": {"nystudio107\\pluginvite\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com"}], "description": "Plugin Vite is the conduit between Craft CMS plugins and Vite, with manifest.json & HMR support", "keywords": ["craftcms", "plugin", "vite"], "support": {"docs": "https://github.com/nystudio107/craft-plugin-vite/blob/v5/README.md", "issues": "https://github.com/nystudio107/craft-plugin-vite/issues", "source": "https://github.com/nystudio107/craft-plugin-vite/tree/5.0.2"}, "funding": [{"url": "https://github.com/khalwat", "type": "github"}], "time": "2024-08-14T02:02:01+00:00"}, {"name": "nystudio107/craft-vite", "version": "5.0.1", "source": {"type": "git", "url": "https://github.com/nystudio107/craft-vite.git", "reference": "a2eaa9beb4abf1add99e8fc7892e8df57c6b49e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-vite/zipball/a2eaa9beb4abf1add99e8fc7892e8df57c6b49e8", "reference": "a2eaa9beb4abf1add99e8fc7892e8df57c6b49e8", "shasum": ""}, "require": {"craftcms/cms": "^5.0.0", "nystudio107/craft-plugin-vite": "^5.0.2", "php": "^8.2"}, "require-dev": {"craftcms/cloud": "^2.0.0", "craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main", "craftcms/rector": "dev-main"}, "type": "craft-plugin", "extra": {"name": "Vite", "class": "nystudio107\\vite\\Vite", "handle": "vite", "changelogUrl": "https://raw.githubusercontent.com/nystudio107/craft-vite/v5/CHANGELOG.md"}, "autoload": {"psr-4": {"nystudio107\\vite\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com"}], "description": "Allows the use of the Vite.js next generation frontend tooling with Craft CMS", "keywords": ["Craft", "cms", "craft-plugin", "craftcms", "vite"], "support": {"docs": "https://nystudio107.com/docs/vite/", "issues": "https://nystudio107.com/plugins/vite/support", "source": "https://github.com/nystudio107/craft-vite"}, "funding": [{"url": "https://github.com/khalwat", "type": "github"}], "time": "2024-08-14T02:05:18+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/df1e7fde177501eee2037dd159cf04f5f301a512", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512", "shasum": ""}, "require": {"php": "^8"}, "require-dev": {"phpunit/phpunit": "^9", "vimeo/psalm": "^4|^5"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2024-05-08T12:36:18+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "phenx/php-font-lib", "version": "0.5.6", "source": {"type": "git", "url": "https://github.com/dompdf/php-font-lib.git", "reference": "a1681e9793040740a405ac5b189275059e2a9863"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-font-lib/zipball/a1681e9793040740a405ac5b189275059e2a9863", "reference": "a1681e9793040740a405ac5b189275059e2a9863", "shasum": ""}, "require": {"ext-mbstring": "*"}, "require-dev": {"symfony/phpunit-bridge": "^3 || ^4 || ^5 || ^6"}, "type": "library", "autoload": {"psr-4": {"FontLib\\": "src/FontLib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse, export and make subsets of different types of font files.", "homepage": "https://github.com/PhenX/php-font-lib", "support": {"issues": "https://github.com/dompdf/php-font-lib/issues", "source": "https://github.com/dompdf/php-font-lib/tree/0.5.6"}, "time": "2024-01-29T14:45:26+00:00"}, {"name": "phenx/php-svg-lib", "version": "0.5.4", "source": {"type": "git", "url": "https://github.com/dompdf/php-svg-lib.git", "reference": "46b25da81613a9cf43c83b2a8c2c1bdab27df691"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-svg-lib/zipball/46b25da81613a9cf43c83b2a8c2c1bdab27df691", "reference": "46b25da81613a9cf43c83b2a8c2c1bdab27df691", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^8.0", "sabberworm/php-css-parser": "^8.4"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.5 || ^9.5"}, "type": "library", "autoload": {"psr-4": {"Svg\\": "src/Svg"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse and export to PDF SVG files.", "homepage": "https://github.com/PhenX/php-svg-lib", "support": {"issues": "https://github.com/dompdf/php-svg-lib/issues", "source": "https://github.com/dompdf/php-svg-lib/tree/0.5.4"}, "time": "2024-04-08T12:52:34+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.6.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/92dde6a5919e34835c506ac8c523ef095a95ed62", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62", "shasum": ""}, "require": {"doctrine/deprecations": "^1.1", "ext-filter": "*", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.7", "phpstan/phpdoc-parser": "^1.7|^2.0", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.5 || ~1.6.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^9.5", "psalm/phar": "^5.26"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.6.2"}, "time": "2025-04-13T19:20:35+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/679e3ce485b99e84c775d28e2e96fade9a7fb50a", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.18|^2.0"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.10.0"}, "time": "2024-11-09T15:12:26+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "3.9.1", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "56f334ace62fc8301c98d425272cbf0a00eb848d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/56f334ace62fc8301c98d425272cbf0a00eb848d", "reference": "56f334ace62fc8301c98d425272cbf0a00eb848d", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "maennchen/zipstream-php": "^2.1 || ^3.0", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^8.1", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-main", "dompdf/dompdf": "^2.0 || ^3.0", "friendsofphp/php-cs-fixer": "^3.2", "mitoteam/jpgraph": "^10.3", "mpdf/mpdf": "^8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^10.5", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "ext-intl": "PHP Internationalization Functions", "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/3.9.1"}, "time": "2025-02-08T03:04:52+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.3", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/e3fac8b24f56113f7cb96af14958c0dd16330f54", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2024-07-20T21:41:07+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^5.3.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.1.0"}, "time": "2025-02-19T13:28:12+00:00"}, {"name": "pixelandtonic/imagine", "version": "*******", "source": {"type": "git", "url": "https://github.com/pixelandtonic/Imagine.git", "reference": "4d9bb596ff60504e37ccf9103c0bb705dba7fec6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pixelandtonic/Imagine/zipball/4d9bb596ff60504e37ccf9103c0bb705dba7fec6", "reference": "4d9bb596ff60504e37ccf9103c0bb705dba7fec6", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.7 || ^6.5 || ^7.5 || ^8.4 || ^9.3"}, "suggest": {"ext-exif": "to read EXIF metadata", "ext-gd": "to use the GD implementation", "ext-gmagick": "to use the Gmagick implementation", "ext-imagick": "to use the Imagick implementation"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "1.x-dev"}}, "autoload": {"psr-4": {"Imagine\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://avalanche123.com"}], "description": "Image processing for PHP 5.3", "homepage": "http://imagine.readthedocs.org/", "keywords": ["drawing", "graphics", "image manipulation", "image processing"], "support": {"source": "https://github.com/pixelandtonic/Imagine/tree/*******"}, "time": "2023-01-03T19:18:06+00:00"}, {"name": "pragmarx/google2fa", "version": "v8.0.3", "source": {"type": "git", "url": "https://github.com/antonioribeiro/google2fa.git", "reference": "6f8d87ebd5afbf7790bde1ffc7579c7c705e0fad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/google2fa/zipball/6f8d87ebd5afbf7790bde1ffc7579c7c705e0fad", "reference": "6f8d87ebd5afbf7790bde1ffc7579c7c705e0fad", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1.0|^2.0|^3.0", "php": "^7.1|^8.0"}, "require-dev": {"phpstan/phpstan": "^1.9", "phpunit/phpunit": "^7.5.15|^8.5|^9.0"}, "type": "library", "autoload": {"psr-4": {"PragmaRX\\Google2FA\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator & Designer"}], "description": "A One Time Password Authentication package, compatible with Google Authenticator.", "keywords": ["2fa", "Authentication", "Two Factor Authentication", "google2fa"], "support": {"issues": "https://github.com/antonioribeiro/google2fa/issues", "source": "https://github.com/antonioribeiro/google2fa/tree/v8.0.3"}, "time": "2024-09-05T11:56:40+00:00"}, {"name": "pragmarx/random", "version": "v0.2.2", "source": {"type": "git", "url": "https://github.com/antonioribeiro/random.git", "reference": "daf08a189c5d2d40d1a827db46364d3a741a51b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/random/zipball/daf08a189c5d2d40d1a827db46364d3a741a51b7", "reference": "daf08a189c5d2d40d1a827db46364d3a741a51b7", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"fzaninotto/faker": "~1.7", "phpunit/phpunit": "~6.4", "pragmarx/trivia": "~0.1", "squizlabs/php_codesniffer": "^2.3"}, "suggest": {"fzaninotto/faker": "Allows you to get dozens of randomized types", "pragmarx/trivia": "For the trivia database"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"PragmaRX\\Random\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://antoniocarlosribeiro.com", "role": "Developer"}], "description": "Create random chars, numbers, strings", "homepage": "https://github.com/antonioribeiro/random", "keywords": ["Randomize", "faker", "pragmarx", "random", "random number", "random pattern", "random string"], "support": {"issues": "https://github.com/antonioribeiro/random/issues", "source": "https://github.com/antonioribeiro/random/tree/master"}, "time": "2017-11-21T05:26:22+00:00"}, {"name": "pragmarx/recovery", "version": "v0.2.1", "source": {"type": "git", "url": "https://github.com/antonioribeiro/recovery.git", "reference": "b5ce4082f059afac6761714a84497816f45271cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/recovery/zipball/b5ce4082f059afac6761714a84497816f45271cc", "reference": "b5ce4082f059afac6761714a84497816f45271cc", "shasum": ""}, "require": {"php": ">=7.0", "pragmarx/random": "~0.1"}, "require-dev": {"phpunit/phpunit": ">=5.4.3", "squizlabs/php_codesniffer": "^2.3", "tightenco/collect": "^5.0"}, "suggest": {"tightenco/collect": "Allows to generate recovery codes as collections"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"PragmaRX\\Recovery\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://antoniocarlosribeiro.com", "role": "Developer"}], "description": "Create recovery codes for two factor auth", "homepage": "https://github.com/antonioribeiro/recovery", "keywords": ["2fa", "account recovery", "auth", "backup codes", "google2fa", "pragmarx", "recovery", "recovery codes", "two factor auth"], "support": {"issues": "https://github.com/antonioribeiro/recovery/issues", "source": "https://github.com/antonioribeiro/recovery/tree/v0.2.1"}, "time": "2021-08-15T12:26:51+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "sabberworm/php-css-parser", "version": "v8.8.0", "source": {"type": "git", "url": "https://github.com/MyIntervals/PHP-CSS-Parser.git", "reference": "3de493bdddfd1f051249af725c7e0d2c38fed740"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/PHP-CSS-Parser/zipball/3de493bdddfd1f051249af725c7e0d2c38fed740", "reference": "3de493bdddfd1f051249af725c7e0d2c38fed740", "shasum": ""}, "require": {"ext-iconv": "*", "php": "^5.6.20 || ^7.0.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"phpunit/phpunit": "5.7.27 || 6.5.14 || 7.5.20 || 8.5.41"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "type": "library", "extra": {"branch-alias": {"dev-main": "9.0.x-dev"}}, "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/MyIntervals/PHP-CSS-Parser/issues", "source": "https://github.com/MyIntervals/PHP-CSS-Parser/tree/v8.8.0"}, "time": "2025-03-23T17:59:05+00:00"}, {"name": "samdark/yii2-psr-log-target", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/samdark/yii2-psr-log-target.git", "reference": "5f14f21d5ee4294fe9eb3e723ec8a3908ca082ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/samdark/yii2-psr-log-target/zipball/5f14f21d5ee4294fe9eb3e723ec8a3908ca082ea", "reference": "5f14f21d5ee4294fe9eb3e723ec8a3908ca082ea", "shasum": ""}, "require": {"psr/log": "~1.0.2|~1.1.0|~3.0.0", "yiisoft/yii2": "~2.0.0"}, "require-dev": {"phpunit/phpunit": "~4.4|~10.4.2"}, "type": "yii2-extension", "autoload": {"psr-4": {"samdark\\log\\": "src", "samdark\\log\\tests\\": "tests"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Yii 2 log target which uses PSR-3 compatible logger", "homepage": "https://github.com/samdark/yii2-psr-log-target", "keywords": ["extension", "log", "psr-3", "yii"], "support": {"issues": "https://github.com/samdark/yii2-psr-log-target/issues", "source": "https://github.com/samdark/yii2-psr-log-target"}, "funding": [{"url": "https://github.com/samdark", "type": "github"}, {"url": "https://www.patreon.com/samdark", "type": "patreon"}], "time": "2023-11-23T14:11:29+00:00"}, {"name": "seld/cli-prompt", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/Seldaek/cli-prompt.git", "reference": "b8dfcf02094b8c03b40322c229493bb2884423c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/cli-prompt/zipball/b8dfcf02094b8c03b40322c229493bb2884423c5", "reference": "b8dfcf02094b8c03b40322c229493bb2884423c5", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpstan/phpstan": "^0.12.63"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\CliPrompt\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Allows you to prompt for user input on the command line, and optionally hide the characters they type", "keywords": ["cli", "console", "hidden", "input", "prompt"], "support": {"issues": "https://github.com/Seldaek/cli-prompt/issues", "source": "https://github.com/Seldaek/cli-prompt/tree/1.0.4"}, "time": "2020-12-15T21:32:01+00:00"}, {"name": "solspace/craft-freeform", "version": "5.10.14", "source": {"type": "git", "url": "https://github.com/solspace/craft-freeform.git", "reference": "d0c0da40ae34b9e0963d9c7a3e7ba0f0fc6bbdf0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/solspace/craft-freeform/zipball/d0c0da40ae34b9e0963d9c7a3e7ba0f0fc6bbdf0", "reference": "d0c0da40ae34b9e0963d9c7a3e7ba0f0fc6bbdf0", "shasum": ""}, "require": {"composer/class-map-generator": "^1.1", "craftcms/cms": "^4.0.0|^5.0.0", "doctrine/annotations": "^2.0", "dompdf/dompdf": "^1.0|^2.0", "egulias/email-validator": "^2.1|^3.0|^4.0", "ext-json": "*", "ext-zip": "*", "fakerphp/faker": "^1.0", "hashids/hashids": "^3.0|^4.0|^5.0", "nesbot/carbon": "^1.22.1|^2.19|^3.0.0", "php": "^8.0.2|^8.2.0", "phpoffice/phpspreadsheet": "^1.29|^2.0|^3.0", "stripe/stripe-php": "^7.0|^8.0|^9.0|^10.0|^12.0|^13.0|^14.0|^15.0", "symfony/expression-language": "^5.0|^6.0|^7.0", "symfony/filesystem": "^5.0|^6.0|^7.0", "symfony/finder": "^5.0|^6.0|^7.0", "symfony/property-access": "^5.0|^6.0|^7.0", "symfony/serializer": "^5.0|^6.0|^7.0", "tecnickcom/tcpdf": "^6.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "^v3.52.0", "phpcompatibility/php-compatibility": "dev-develop", "phpunit/phpunit": "^11.5.1"}, "type": "craft-plugin", "extra": {"name": "Freeform", "class": "Solspace\\Freeform\\Freeform", "handle": "freeform", "developer": "Solspace", "hasSettings": true, "changelogUrl": "https://raw.githubusercontent.com/solspace/craft-freeform/v5/CHANGELOG.md", "developerUrl": "https://docs.solspace.com/", "hasCpSection": true, "schemaVersion": "5.4.2", "documentationUrl": "https://docs.solspace.com/craft/freeform/v5/"}, "autoload": {"psr-4": {"Solspace\\Freeform\\": "packages/plugin/src/"}}, "notification-url": "https://packagist.org/downloads/", "authors": [{"name": "Solspace", "homepage": "https://solspace.com/"}], "description": "The most flexible and user-friendly form building plugin!", "support": {"issues": "https://github.com/solspace/craft-freeform/issues", "source": "https://github.com/solspace/craft-freeform/tree/v5.10.14"}, "time": "2025-05-13T19:41:41+00:00"}, {"name": "spomky-labs/cbor-php", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/Spomky-Labs/cbor-php.git", "reference": "499d9bff0a6d59c4f1b813cc617fc3fd56d6dca4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Spomky-Labs/cbor-php/zipball/499d9bff0a6d59c4f1b813cc617fc3fd56d6dca4", "reference": "499d9bff0a6d59c4f1b813cc617fc3fd56d6dca4", "shasum": ""}, "require": {"brick/math": "^0.9|^0.10|^0.11|^0.12", "ext-mbstring": "*", "php": ">=8.0"}, "require-dev": {"ekino/phpstan-banned-code": "^1.0", "ext-json": "*", "infection/infection": "^0.29", "php-parallel-lint/php-parallel-lint": "^1.3", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.0", "phpstan/phpstan-beberlei-assert": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^10.1|^11.0", "qossmic/deptrac": "^2.0", "rector/rector": "^1.0", "roave/security-advisories": "dev-latest", "symfony/var-dumper": "^6.0|^7.0", "symplify/easy-coding-standard": "^12.0"}, "suggest": {"ext-bcmath": "GMP or BCMath extensions will drastically improve the library performance. BCMath extension needed to handle the Big Float and Decimal Fraction Tags", "ext-gmp": "GMP or BCMath extensions will drastically improve the library performance"}, "type": "library", "autoload": {"psr-4": {"CBOR\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Florent <PERSON>", "homepage": "https://github.com/Spomky"}, {"name": "All contributors", "homepage": "https://github.com/Spomky-Labs/cbor-php/contributors"}], "description": "CBOR Encoder/Decoder for PHP", "keywords": ["Concise Binary Object Representation", "RFC7049", "cbor"], "support": {"issues": "https://github.com/Spomky-Labs/cbor-php/issues", "source": "https://github.com/Spomky-Labs/cbor-php/tree/3.1.0"}, "funding": [{"url": "https://github.com/Spomky", "type": "github"}, {"url": "https://www.patreon.com/Florent<PERSON>orselli", "type": "patreon"}], "time": "2024-07-18T08:37:03+00:00"}, {"name": "spomky-labs/pki-framework", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/Spomky-Labs/pki-framework.git", "reference": "5ff1dcc21e961b60149a80e77f744fc047800b31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Spomky-Labs/pki-framework/zipball/5ff1dcc21e961b60149a80e77f744fc047800b31", "reference": "5ff1dcc21e961b60149a80e77f744fc047800b31", "shasum": ""}, "require": {"brick/math": "^0.10|^0.11|^0.12|^0.13", "ext-mbstring": "*", "php": ">=8.1"}, "require-dev": {"ekino/phpstan-banned-code": "^1.0|^2.0|^3.0", "ext-gmp": "*", "ext-openssl": "*", "infection/infection": "^0.28|^0.29", "php-parallel-lint/php-parallel-lint": "^1.3", "phpstan/extension-installer": "^1.3|^2.0", "phpstan/phpstan": "^1.8|^2.0", "phpstan/phpstan-deprecation-rules": "^1.0|^2.0", "phpstan/phpstan-phpunit": "^1.1|^2.0", "phpstan/phpstan-strict-rules": "^1.3|^2.0", "phpunit/phpunit": "^10.1|^11.0|^12.0", "rector/rector": "^1.0|^2.0", "roave/security-advisories": "dev-latest", "symfony/string": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0", "symplify/easy-coding-standard": "^12.0"}, "suggest": {"ext-bcmath": "For better performance (or GMP)", "ext-gmp": "For better performance (or BCMath)", "ext-openssl": "For OpenSSL based cyphering"}, "type": "library", "autoload": {"psr-4": {"SpomkyLabs\\Pki\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Original developer"}, {"name": "Florent <PERSON>", "email": "<EMAIL>", "role": "Spomky-Labs PKI Framework developer"}], "description": "A PHP framework for managing Public Key Infrastructures. It comprises X.509 public key certificates, attribute certificates, certification requests and certification path validation.", "homepage": "https://github.com/spomky-labs/pki-framework", "keywords": ["DER", "Private Key", "ac", "algorithm identifier", "asn.1", "asn1", "attribute certificate", "certificate", "certification request", "cryptography", "csr", "decrypt", "ec", "encrypt", "pem", "pkcs", "public key", "rsa", "sign", "signature", "verify", "x.509", "x.690", "x509", "x690"], "support": {"issues": "https://github.com/Spomky-Labs/pki-framework/issues", "source": "https://github.com/Spomky-Labs/pki-framework/tree/1.2.3"}, "funding": [{"url": "https://github.com/Spomky", "type": "github"}, {"url": "https://www.patreon.com/Florent<PERSON>orselli", "type": "patreon"}], "time": "2025-04-25T15:57:13+00:00"}, {"name": "stripe/stripe-php", "version": "v15.10.0", "source": {"type": "git", "url": "https://github.com/stripe/stripe-php.git", "reference": "****************************************"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stripe/stripe-php/zipball/****************************************", "reference": "****************************************", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=5.6.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.5.0", "phpstan/phpstan": "^1.2", "phpunit/phpunit": "^5.7 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Stripe\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Stripe and contributors", "homepage": "https://github.com/stripe/stripe-php/contributors"}], "description": "Stripe PHP Library", "homepage": "https://stripe.com/", "keywords": ["api", "payment processing", "stripe"], "support": {"issues": "https://github.com/stripe/stripe-php/issues", "source": "https://github.com/stripe/stripe-php/tree/v15.10.0"}, "time": "2024-09-18T18:38:30+00:00"}, {"name": "symfony/cache", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "d1abcf763a7414f2e572f676f22da7a06c8cd9ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/d1abcf763a7414f2e572f676f22da7a06c8cd9ee", "reference": "d1abcf763a7414f2e572f676f22da7a06c8cd9ee", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.3.6|^7.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/var-dumper": "<5.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-08T08:21:20+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "15a4f8e5cd3bce9aeafc882b1acab39ec8de2c1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/15a4f8e5cd3bce9aeafc882b1acab39ec8de2c1b", "reference": "15a4f8e5cd3bce9aeafc882b1acab39ec8de2c1b", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/clock", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/clock.git", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/clock/zipball/b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "shasum": ""}, "require": {"php": ">=8.2", "psr/clock": "^1.0", "symfony/polyfill-php83": "^1.28"}, "provide": {"psr/clock-implementation": "1.0"}, "type": "library", "autoload": {"files": ["Resources/now.php"], "psr-4": {"Symfony\\Component\\Clock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Decouples applications from the system clock", "homepage": "https://symfony.com", "keywords": ["clock", "psr20", "time"], "support": {"source": "https://github.com/symfony/clock/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/css-selector", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/601a5ce9aaad7bf10797e3663faefce9e26c24e2", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/dom-crawler", "version": "v7.2.4", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "19cc7b08efe9ad1ab1b56e0948e8d02e15ed3ef7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/19cc7b08efe9ad1ab1b56e0948e8d02e15ed3ef7", "reference": "19cc7b08efe9ad1ab1b56e0948e8d02e15ed3ef7", "shasum": ""}, "require": {"masterminds/html5": "^2.6", "php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/css-selector": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v7.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-17T15:53:07+00:00"}, {"name": "symfony/event-dispatcher", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/910c5db85a5356d0fea57680defec4e99eb9c8c1", "reference": "910c5db85a5356d0fea57680defec4e99eb9c8c1", "shasum": ""}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7642f5e970b672283b7823222ae8ef8bbc160b9f", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/expression-language", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "26f4884a455e755e630a5fc372df124a3578da2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/26f4884a455e755e630a5fc372df124a3578da2e", "reference": "26f4884a455e755e630a5fc372df124a3578da2e", "shasum": ""}, "require": {"php": ">=8.2", "symfony/cache": "^6.4|^7.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an engine that can compile and evaluate expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/expression-language/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-15T11:52:45+00:00"}, {"name": "symfony/filesystem", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/4856c9cf585d5a0313d8d35afd681a526f038dd3", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^5.4|^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:07:50+00:00"}, {"name": "symfony/finder", "version": "v7.2.2", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "87a71856f2f56e4100373e92529eed3171695cfb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/87a71856f2f56e4100373e92529eed3171695cfb", "reference": "87a71856f2f56e4100373e92529eed3171695cfb", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.2.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-30T19:00:17+00:00"}, {"name": "symfony/http-client", "version": "v7.2.4", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "78981a2ffef6437ed92d4d7e2a86a82f256c6dc6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/78981a2ffef6437ed92d4d7e2a86a82f256c6dc6", "reference": "78981a2ffef6437ed92d4d7e2a86a82f256c6dc6", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "~3.4.4|^3.5.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"amphp/amp": "<2.5", "php-http/discovery": "<1.15", "symfony/http-foundation": "<6.4"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}, "require-dev": {"amphp/http-client": "^4.2.1|^5.0", "amphp/http-tunnel": "^1.0|^2.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/amphp-http-client-meta": "^1.0|^2.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v7.2.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-13T10:27:23+00:00"}, {"name": "symfony/http-client-contracts", "version": "v3.5.2", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "ee8d807ab20fcb51267fdace50fbe3494c31e645"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/ee8d807ab20fcb51267fdace50fbe3494c31e645", "reference": "ee8d807ab20fcb51267fdace50fbe3494c31e645", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-07T08:49:48+00:00"}, {"name": "symfony/mailer", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "998692469d6e698c6eadc7ef37a6530a9eabb356"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/998692469d6e698c6eadc7ef37a6530a9eabb356", "reference": "998692469d6e698c6eadc7ef37a6530a9eabb356", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.2", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/mime": "^7.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/messenger": "<6.4", "symfony/mime": "<6.4", "symfony/twig-bridge": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/twig-bridge": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-04T09:50:51+00:00"}, {"name": "symfony/mime", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "706e65c72d402539a072d0d6ad105fff6c161ef1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/706e65c72d402539a072d0d6ad105fff6c161ef1", "reference": "706e65c72d402539a072d0d6ad105fff6c161ef1", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T13:34:41+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "5f3b930437ae03ae5dff61269024d8ea1b3774aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/5f3b930437ae03ae5dff61269024d8ea1b3774aa", "reference": "5f3b930437ae03ae5dff61269024d8ea1b3774aa", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-17T14:58:18+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/9614ac4d8061dc257ecc64cba1b140873dce8ad3", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-10T14:38:51+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "metapackage", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T08:10:11+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php84", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php84.git", "reference": "000df7860439609837bbe28670b0be15783b7fbf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php84/zipball/000df7860439609837bbe28670b0be15783b7fbf", "reference": "000df7860439609837bbe28670b0be15783b7fbf", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php84\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.4+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php84/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-20T12:04:08+00:00"}, {"name": "symfony/polyfill-uuid", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-uuid.git", "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/21533be36c24be3f4b1669c4725c7d1d2bab4ae2", "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-uuid": "*"}, "suggest": {"ext-uuid": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Uuid\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for uuid functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "uuid"], "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/process", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "87b7c93e57df9d8e39a093d32587702380ff045d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/87b7c93e57df9d8e39a093d32587702380ff045d", "reference": "87b7c93e57df9d8e39a093d32587702380ff045d", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T12:21:46+00:00"}, {"name": "symfony/property-access", "version": "v7.2.3", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "b28732e315d81fbec787f838034de7d6c9b2b902"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/b28732e315d81fbec787f838034de7d6c9b2b902", "reference": "b28732e315d81fbec787f838034de7d6c9b2b902", "shasum": ""}, "require": {"php": ">=8.2", "symfony/property-info": "^6.4|^7.0"}, "require-dev": {"symfony/cache": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property-path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v7.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-17T10:56:55+00:00"}, {"name": "symfony/property-info", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "f00fd9685ecdbabe82ca25c7b739ce7bba99302c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/f00fd9685ecdbabe82ca25c7b739ce7bba99302c", "reference": "f00fd9685ecdbabe82ca25c7b739ce7bba99302c", "shasum": ""}, "require": {"php": ">=8.2", "symfony/string": "^6.4|^7.0", "symfony/type-info": "~7.1.9|^7.2.2"}, "conflict": {"phpdocumentor/reflection-docblock": "<5.2", "phpdocumentor/type-resolver": "<1.5.1", "symfony/cache": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/serializer": "<6.4"}, "require-dev": {"phpdocumentor/reflection-docblock": "^5.2", "phpstan/phpdoc-parser": "^1.0|^2.0", "symfony/cache": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-06T16:27:19+00:00"}, {"name": "symfony/serializer", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "c45f8f7763afb11e85772c0c1debb8f272c17f51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/c45f8f7763afb11e85772c0c1debb8f272c17f51", "reference": "c45f8f7763afb11e85772c0c1debb8f272c17f51", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"doctrine/annotations": "<1.12", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<5.4", "symfony/property-access": "<5.4", "symfony/property-info": "<5.4.24|>=6,<6.2.11", "symfony/uid": "<5.4", "symfony/validator": "<6.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "seld/jsonlint": "^1.10", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/form": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4.26|^6.3|^7.0", "symfony/property-info": "^5.4.24|^6.2.11|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^5.4|^6.0|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0", "symfony/var-exporter": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T13:27:38+00:00"}, {"name": "symfony/service-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/string", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "a214fe7d62bd4df2a76447c67c6b26e1d5e74931"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/a214fe7d62bd4df2a76447c67c6b26e1d5e74931", "reference": "a214fe7d62bd4df2a76447c67c6b26e1d5e74931", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-20T20:18:16+00:00"}, {"name": "symfony/translation", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "e7fd8e2a4239b79a0fd9fb1fef3e0e7f969c6dc6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/e7fd8e2a4239b79a0fd9fb1fef3e0e7f969c6dc6", "reference": "e7fd8e2a4239b79a0fd9fb1fef3e0e7f969c6dc6", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-07T19:09:28+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/4667ff3bd513750603a09c8dedbea942487fb07c", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/type-info", "version": "v7.2.5", "source": {"type": "git", "url": "https://github.com/symfony/type-info.git", "reference": "c4824a6b658294c828e609d3d8dbb4e87f6a375d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/type-info/zipball/c4824a6b658294c828e609d3d8dbb4e87f6a375d", "reference": "c4824a6b658294c828e609d3d8dbb4e87f6a375d", "shasum": ""}, "require": {"php": ">=8.2", "psr/container": "^1.1|^2.0"}, "require-dev": {"phpstan/phpdoc-parser": "^1.0|^2.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\TypeInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Baptiste LEDUC", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts PHP types information.", "homepage": "https://symfony.com", "keywords": ["PHPStan", "phpdoc", "symfony", "type"], "support": {"source": "https://github.com/symfony/type-info/tree/v7.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-24T09:03:36+00:00"}, {"name": "symfony/uid", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/uid.git", "reference": "2d294d0c48df244c71c105a169d0190bfb080426"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/uid/zipball/2d294d0c48df244c71c105a169d0190bfb080426", "reference": "2d294d0c48df244c71c105a169d0190bfb080426", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-uuid": "^1.15"}, "require-dev": {"symfony/console": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Uid\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to generate and represent UIDs", "homepage": "https://symfony.com", "keywords": ["UID", "ulid", "uuid"], "support": {"source": "https://github.com/symfony/uid/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/var-dumper", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "42f18f170aa86d612c3559cfb3bd11a375df32c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/42f18f170aa86d612c3559cfb3bd11a375df32c8", "reference": "42f18f170aa86d612c3559cfb3bd11a375df32c8", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/console": "<4.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/uid": "^5.1|^6.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-08T15:21:10+00:00"}, {"name": "symfony/var-exporter", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "422b8de94c738830a1e071f59ad14d67417d7007"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/422b8de94c738830a1e071f59ad14d67417d7007", "reference": "422b8de94c738830a1e071f59ad14d67417d7007", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-02T08:36:00+00:00"}, {"name": "symfony/yaml", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "f01987f45676778b474468aa266fe2eda1f2bc7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/f01987f45676778b474468aa266fe2eda1f2bc7e", "reference": "f01987f45676778b474468aa266fe2eda1f2bc7e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-04T09:48:44+00:00"}, {"name": "tecnickcom/tcpdf", "version": "6.9.4", "source": {"type": "git", "url": "https://github.com/tecnickcom/TCPDF.git", "reference": "c838d7f7babb0d35763acfb9ecf78c3f45966f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tecnickcom/TCPDF/zipball/c838d7f7babb0d35763acfb9ecf78c3f45966f83", "reference": "c838d7f7babb0d35763acfb9ecf78c3f45966f83", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=7.1.0"}, "type": "library", "autoload": {"classmap": ["config", "include", "tcpdf.php", "tcpdf_barcodes_1d.php", "tcpdf_barcodes_2d.php", "include/tcpdf_colors.php", "include/tcpdf_filters.php", "include/tcpdf_font_data.php", "include/tcpdf_fonts.php", "include/tcpdf_images.php", "include/tcpdf_static.php", "include/barcodes/datamatrix.php", "include/barcodes/pdf417.php", "include/barcodes/qrcode.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "TCPDF is a PHP class for generating PDF documents and barcodes.", "homepage": "http://www.tcpdf.org/", "keywords": ["PDFD32000-2008", "TCPDF", "barcodes", "datamatrix", "pdf", "pdf417", "qrcode"], "support": {"issues": "https://github.com/tecnickcom/TCPDF/issues", "source": "https://github.com/tecnickcom/TCPDF/tree/6.9.4"}, "funding": [{"url": "https://www.paypal.com/donate/?hosted_button_id=NZUEC5XS8MFBJ", "type": "custom"}], "time": "2025-05-13T11:34:35+00:00"}, {"name": "theiconic/name-parser", "version": "v1.2.11", "source": {"type": "git", "url": "https://github.com/theiconic/name-parser.git", "reference": "9a54a713bf5b2e7fd990828147d42de16bf8a253"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theiconic/name-parser/zipball/9a54a713bf5b2e7fd990828147d42de16bf8a253", "reference": "9a54a713bf5b2e7fd990828147d42de16bf8a253", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "php-mock/php-mock-phpunit": "^2.1", "phpunit/phpunit": "^7.0"}, "type": "library", "autoload": {"psr-4": {"TheIconic\\NameParser\\": ["src/", "tests/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "The Iconic", "email": "<EMAIL>"}], "description": "PHP library for parsing a string containing a full name into its parts", "support": {"issues": "https://github.com/theiconic/name-parser/issues", "source": "https://github.com/theiconic/name-parser/tree/v1.2.11"}, "time": "2019-11-14T14:08:48+00:00"}, {"name": "twig/twig", "version": "v3.15.0", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "2d5b3964cc21d0188633d7ddce732dc8e874db02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/2d5b3964cc21d0188633d7ddce732dc8e874db02", "reference": "2d5b3964cc21d0188633d7ddce732dc8e874db02", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php81": "^1.29"}, "require-dev": {"psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.4|^7.0"}, "type": "library", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.15.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2024-11-17T15:59:19+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.6.2", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "24ac4c74f91ee2c193fa1aaa5c249cb0822809af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/24ac4c74f91ee2c193fa1aaa5c249cb0822809af", "reference": "24ac4c74f91ee2c193fa1aaa5c249cb0822809af", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.3", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-filter": "*", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "5.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2025-04-30T23:37:27+00:00"}, {"name": "voku/anti-xss", "version": "4.1.42", "source": {"type": "git", "url": "https://github.com/voku/anti-xss.git", "reference": "bca1f8607e55a3c5077483615cd93bd8f11bd675"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/anti-xss/zipball/bca1f8607e55a3c5077483615cd93bd8f11bd675", "reference": "bca1f8607e55a3c5077483615cd93bd8f11bd675", "shasum": ""}, "require": {"php": ">=7.0.0", "voku/portable-utf8": "~6.0.2"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1.x-dev"}}, "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "EllisLab Dev Team", "homepage": "http://ellislab.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.moelleken.org/"}], "description": "anti xss-library", "homepage": "https://github.com/voku/anti-xss", "keywords": ["anti-xss", "clean", "security", "xss"], "support": {"issues": "https://github.com/voku/anti-xss/issues", "source": "https://github.com/voku/anti-xss/tree/4.1.42"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/anti-xss", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/anti-xss", "type": "tidelift"}], "time": "2023-07-03T14:40:46+00:00"}, {"name": "voku/arrayy", "version": "7.9.6", "source": {"type": "git", "url": "https://github.com/voku/Arrayy.git", "reference": "0e20b8c6eef7fc46694a2906e0eae2f9fc11cade"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/Arrayy/zipball/0e20b8c6eef7fc46694a2906e0eae2f9fc11cade", "reference": "0e20b8c6eef7fc46694a2906e0eae2f9fc11cade", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.0.0", "phpdocumentor/reflection-docblock": "~4.3 || ~5.0", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "type": "library", "autoload": {"files": ["src/Create.php"], "psr-4": {"Arrayy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.moelleken.org/", "role": "Maintainer"}], "description": "Array manipulation library for PHP, called Arrayy!", "keywords": ["A<PERSON>yy", "array", "helpers", "manipulation", "methods", "utility", "utils"], "support": {"docs": "https://voku.github.io/Arrayy/", "issues": "https://github.com/voku/Arrayy/issues", "source": "https://github.com/voku/Arrayy"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/arrayy", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/arrayy", "type": "tidelift"}], "time": "2022-12-27T12:58:32+00:00"}, {"name": "voku/email-check", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/voku/email-check.git", "reference": "6ea842920bbef6758b8c1e619fd1710e7a1a2cac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/email-check/zipball/6ea842920bbef6758b8c1e619fd1710e7a1a2cac", "reference": "6ea842920bbef6758b8c1e619fd1710e7a1a2cac", "shasum": ""}, "require": {"php": ">=7.0.0", "symfony/polyfill-intl-idn": "~1.10"}, "require-dev": {"fzaninotto/faker": "~1.7", "phpunit/phpunit": "~6.0 || ~7.0"}, "suggest": {"ext-intl": "Use Intl for best performance"}, "type": "library", "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "email-check (syntax, dns, trash, ...) library", "homepage": "https://github.com/voku/email-check", "keywords": ["check-email", "email", "mail", "mail-check", "validate-email", "validate-email-address", "validate-mail"], "support": {"issues": "https://github.com/voku/email-check/issues", "source": "https://github.com/voku/email-check/tree/3.1.0"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/email-check", "type": "tidelift"}], "time": "2021-01-27T14:14:33+00:00"}, {"name": "voku/portable-ascii", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "b1d923f88091c6bf09699efcd7c8a1b1bfd7351d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/b1d923f88091c6bf09699efcd7c8a1b1bfd7351d", "reference": "b1d923f88091c6bf09699efcd7c8a1b1bfd7351d", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "support": {"issues": "https://github.com/voku/portable-ascii/issues", "source": "https://github.com/voku/portable-ascii/tree/2.0.3"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-ascii", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii", "type": "tidelift"}], "time": "2024-11-21T01:49:47+00:00"}, {"name": "voku/portable-utf8", "version": "6.0.13", "source": {"type": "git", "url": "https://github.com/voku/portable-utf8.git", "reference": "b8ce36bf26593e5c2e81b1850ef0ffb299d2043f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-utf8/zipball/b8ce36bf26593e5c2e81b1850ef0ffb299d2043f", "reference": "b8ce36bf26593e5c2e81b1850ef0ffb299d2043f", "shasum": ""}, "require": {"php": ">=7.0.0", "symfony/polyfill-iconv": "~1.0", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.0", "voku/portable-ascii": "~2.0.0"}, "require-dev": {"phpstan/phpstan": "1.9.*@dev", "phpstan/phpstan-strict-rules": "1.4.*@dev", "phpunit/phpunit": "~6.0 || ~7.0 || ~9.0", "thecodingmachine/phpstan-strict-rules": "1.0.*@dev", "voku/phpstan-rules": "3.1.*@dev"}, "suggest": {"ext-ctype": "Use Ctype for e.g. hexadecimal digit detection", "ext-fileinfo": "Use Fileinfo for better binary file detection", "ext-iconv": "Use iconv for best performance", "ext-intl": "Use Intl for best performance", "ext-json": "Use JSON for string detection", "ext-mbstring": "Use Mbstring for best performance"}, "type": "library", "autoload": {"files": ["bootstrap.php"], "psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["(Apache-2.0 or GPL-2.0)"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "http://pageconfig.com/"}, {"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable UTF-8 library - performance optimized (unicode) string functions for php.", "homepage": "https://github.com/voku/portable-utf8", "keywords": ["UTF", "clean", "php", "unicode", "utf-8", "utf8"], "support": {"issues": "https://github.com/voku/portable-utf8/issues", "source": "https://github.com/voku/portable-utf8/tree/6.0.13"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-utf8", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-utf8", "type": "tidelift"}], "time": "2023-03-08T08:35:38+00:00"}, {"name": "voku/stop-words", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/voku/stop-words.git", "reference": "8e63c0af20f800b1600783764e0ce19e53969f71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/stop-words/zipball/8e63c0af20f800b1600783764e0ce19e53969f71", "reference": "8e63c0af20f800b1600783764e0ce19e53969f71", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Stop-Words via PHP", "keywords": ["stop words", "stop-words"], "support": {"issues": "https://github.com/voku/stop-words/issues", "source": "https://github.com/voku/stop-words/tree/master"}, "time": "2018-11-23T01:37:27+00:00"}, {"name": "voku/stringy", "version": "6.5.3", "source": {"type": "git", "url": "https://github.com/voku/Stringy.git", "reference": "c453c88fbff298f042c836ef44306f8703b2d537"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/Stringy/zipball/c453c88fbff298f042c836ef44306f8703b2d537", "reference": "c453c88fbff298f042c836ef44306f8703b2d537", "shasum": ""}, "require": {"defuse/php-encryption": "~2.0", "ext-json": "*", "php": ">=7.0.0", "voku/anti-xss": "~4.1", "voku/arrayy": "~7.8", "voku/email-check": "~3.1", "voku/portable-ascii": "~2.0", "voku/portable-utf8": "~6.0", "voku/urlify": "~5.0"}, "replace": {"danielstjules/stringy": "~3.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "type": "library", "autoload": {"files": ["src/Create.php"], "psr-4": {"Stringy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.danielstjules.com", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.moelleken.org/", "role": "Fork-Maintainer"}], "description": "A string manipulation library with multibyte support", "homepage": "https://github.com/danielstjules/Stringy", "keywords": ["UTF", "helpers", "manipulation", "methods", "multibyte", "string", "utf-8", "utility", "utils"], "support": {"issues": "https://github.com/voku/Stringy/issues", "source": "https://github.com/voku/Stringy"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/stringy", "type": "tidelift"}], "time": "2022-03-28T14:52:20+00:00"}, {"name": "voku/urlify", "version": "5.0.7", "source": {"type": "git", "url": "https://github.com/voku/urlify.git", "reference": "014b2074407b5db5968f836c27d8731934b330e4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/urlify/zipball/014b2074407b5db5968f836c27d8731934b330e4", "reference": "014b2074407b5db5968f836c27d8731934b330e4", "shasum": ""}, "require": {"php": ">=7.0.0", "voku/portable-ascii": "~2.0", "voku/portable-utf8": "~6.0", "voku/stop-words": "~2.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "type": "library", "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.johnnybroadway.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://moelleken.org/"}], "description": "PHP port of URLify.js from the Django project. Transliterates non-ascii characters for use in URLs.", "homepage": "https://github.com/voku/urlify", "keywords": ["encode", "iconv", "link", "slug", "translit", "transliterate", "transliteration", "url", "urlify"], "support": {"issues": "https://github.com/voku/urlify/issues", "source": "https://github.com/voku/urlify/tree/5.0.7"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/urlify", "type": "tidelift"}], "time": "2022-01-24T19:08:46+00:00"}, {"name": "web-auth/cose-lib", "version": "4.4.0", "source": {"type": "git", "url": "https://github.com/web-auth/cose-lib.git", "reference": "2166016e48e0214f4f63320a7758a9386d14c92a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/web-auth/cose-lib/zipball/2166016e48e0214f4f63320a7758a9386d14c92a", "reference": "2166016e48e0214f4f63320a7758a9386d14c92a", "shasum": ""}, "require": {"brick/math": "^0.9|^0.10|^0.11|^0.12", "ext-json": "*", "ext-openssl": "*", "php": ">=8.1", "spomky-labs/pki-framework": "^1.0"}, "require-dev": {"ekino/phpstan-banned-code": "^1.0", "infection/infection": "^0.29", "php-parallel-lint/php-parallel-lint": "^1.3", "phpstan/extension-installer": "^1.3", "phpstan/phpstan": "^1.7", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.2", "phpunit/phpunit": "^10.1|^11.0", "qossmic/deptrac": "^2.0", "rector/rector": "^1.0", "symfony/phpunit-bridge": "^6.4|^7.0", "symplify/easy-coding-standard": "^12.0"}, "suggest": {"ext-bcmath": "For better performance, please install either GMP (recommended) or BCMath extension", "ext-gmp": "For better performance, please install either GMP (recommended) or BCMath extension"}, "type": "library", "autoload": {"psr-4": {"Cose\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Florent <PERSON>", "homepage": "https://github.com/Spomky"}, {"name": "All contributors", "homepage": "https://github.com/web-auth/cose/contributors"}], "description": "CBOR Object Signing and Encryption (COSE) For PHP", "homepage": "https://github.com/web-auth", "keywords": ["COSE", "RFC8152"], "support": {"issues": "https://github.com/web-auth/cose-lib/issues", "source": "https://github.com/web-auth/cose-lib/tree/4.4.0"}, "funding": [{"url": "https://github.com/Spomky", "type": "github"}, {"url": "https://www.patreon.com/Florent<PERSON>orselli", "type": "patreon"}], "time": "2024-07-18T08:47:32+00:00"}, {"name": "web-auth/webauthn-lib", "version": "4.9.2", "source": {"type": "git", "url": "https://github.com/web-auth/webauthn-lib.git", "reference": "008b25171c27cf4813420d0de31cc059bcc71f1a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/web-auth/webauthn-lib/zipball/008b25171c27cf4813420d0de31cc059bcc71f1a", "reference": "008b25171c27cf4813420d0de31cc059bcc71f1a", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "lcobucci/clock": "^2.2|^3.0", "paragonie/constant_time_encoding": "^2.6|^3.0", "php": ">=8.1", "psr/clock": "^1.0", "psr/event-dispatcher": "^1.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/log": "^1.0|^2.0|^3.0", "spomky-labs/cbor-php": "^3.0", "spomky-labs/pki-framework": "^1.0", "symfony/deprecation-contracts": "^3.2", "symfony/uid": "^6.1|^7.0", "web-auth/cose-lib": "^4.2.3"}, "suggest": {"phpdocumentor/reflection-docblock": "As of 4.5.x, the phpdocumentor/reflection-docblock component will become mandatory for converting objects such as the Metadata Statement", "psr/clock-implementation": "As of 4.5.x, the PSR Clock implementation will replace lcobucci/clock", "psr/log-implementation": "Recommended to receive logs from the library", "symfony/event-dispatcher": "Recommended to use dispatched events", "symfony/property-access": "As of 4.5.x, the symfony/serializer component will become mandatory for converting objects such as the Metadata Statement", "symfony/property-info": "As of 4.5.x, the symfony/serializer component will become mandatory for converting objects such as the Metadata Statement", "symfony/serializer": "As of 4.5.x, the symfony/serializer component will become mandatory for converting objects such as the Metadata Statement", "web-token/jwt-library": "Mandatory for fetching Metadata Statement from distant sources"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/web-auth/webauthn-framework", "name": "web-auth/webauthn-framework"}}, "autoload": {"psr-4": {"Webauthn\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Florent <PERSON>", "homepage": "https://github.com/Spomky"}, {"name": "All contributors", "homepage": "https://github.com/web-auth/webauthn-library/contributors"}], "description": "FIDO2/Webauthn Support For PHP", "homepage": "https://github.com/web-auth", "keywords": ["FIDO2", "fido", "webauthn"], "support": {"source": "https://github.com/web-auth/webauthn-lib/tree/4.9.2"}, "funding": [{"url": "https://github.com/Spomky", "type": "github"}, {"url": "https://www.patreon.com/Florent<PERSON>orselli", "type": "patreon"}], "time": "2025-01-04T09:47:58+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "webonyx/graphql-php", "version": "v14.11.10", "source": {"type": "git", "url": "https://github.com/webonyx/graphql-php.git", "reference": "d9c2fdebc6aa01d831bc2969da00e8588cffef19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webonyx/graphql-php/zipball/d9c2fdebc6aa01d831bc2969da00e8588cffef19", "reference": "d9c2fdebc6aa01d831bc2969da00e8588cffef19", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^7.1 || ^8"}, "require-dev": {"amphp/amp": "^2.3", "doctrine/coding-standard": "^6.0", "nyholm/psr7": "^1.2", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "0.12.82", "phpstan/phpstan-phpunit": "0.12.18", "phpstan/phpstan-strict-rules": "0.12.9", "phpunit/phpunit": "^7.2 || ^8.5", "psr/http-message": "^1.0", "react/promise": "2.*", "simpod/php-coveralls-mirror": "^3.0"}, "suggest": {"psr/http-message": "To use standard GraphQL server", "react/promise": "To leverage async resolving on React PHP platform"}, "type": "library", "autoload": {"psr-4": {"GraphQL\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP port of GraphQL reference implementation", "homepage": "https://github.com/webonyx/graphql-php", "keywords": ["api", "graphql"], "support": {"issues": "https://github.com/webonyx/graphql-php/issues", "source": "https://github.com/webonyx/graphql-php/tree/v14.11.10"}, "funding": [{"url": "https://opencollective.com/webonyx-graphql-php", "type": "open_collective"}], "time": "2023-07-05T14:23:37+00:00"}, {"name": "yiisoft/yii2", "version": "2.0.52", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-framework.git", "reference": "540e7387d934c52e415614aa081fb38d04c72d9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-framework/zipball/540e7387d934c52e415614aa081fb38d04c72d9a", "reference": "540e7387d934c52e415614aa081fb38d04c72d9a", "shasum": ""}, "require": {"bower-asset/inputmask": "^5.0.8 ", "bower-asset/jquery": "3.7.*@stable | 3.6.*@stable | 3.5.*@stable | 3.4.*@stable | 3.3.*@stable | 3.2.*@stable | 3.1.*@stable | 2.2.*@stable | 2.1.*@stable | 1.11.*@stable | 1.12.*@stable", "bower-asset/punycode": "^1.4", "bower-asset/yii2-pjax": "~2.0.1", "cebe/markdown": "~1.0.0 | ~1.1.0 | ~1.2.0", "ext-ctype": "*", "ext-mbstring": "*", "ezyang/htmlpurifier": "^4.17", "lib-pcre": "*", "php": ">=7.3.0", "yiisoft/yii2-composer": "~2.0.4"}, "bin": ["yii"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.yiiframework.com/", "role": "Founder and project lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://rmcreative.ru/", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://mdomba.info/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.cebe.cc/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://resurtm.com/", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://dynasource.eu", "role": "Core framework development"}], "description": "Yii PHP Framework Version 2", "homepage": "https://www.yiiframework.com/", "keywords": ["framework", "yii2"], "support": {"forum": "https://forum.yiiframework.com/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2/issues?state=open", "source": "https://github.com/yiisoft/yii2", "wiki": "https://www.yiiframework.com/wiki"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2", "type": "tidelift"}], "time": "2025-02-13T20:02:28+00:00"}, {"name": "yiisoft/yii2-composer", "version": "2.0.11", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-composer.git", "reference": "b684b01ecb119c8287721def726a0e24fec2fef2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-composer/zipball/b684b01ecb119c8287721def726a0e24fec2fef2", "reference": "b684b01ecb119c8287721def726a0e24fec2fef2", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 | ^2.0"}, "require-dev": {"composer/composer": "^1.0 | ^2.0@dev", "phpunit/phpunit": "<7"}, "type": "composer-plugin", "extra": {"class": "yii\\composer\\Plugin", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\composer\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The composer plugin for Yii extension installer", "keywords": ["composer", "extension installer", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-composer/issues", "source": "https://github.com/yiisoft/yii2-composer", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-composer", "type": "tidelift"}], "time": "2025-02-13T20:59:36+00:00"}, {"name": "yiisoft/yii2-debug", "version": "2.1.26", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-debug.git", "reference": "e4b28a1d295fc977d8399db544336dd5b2764397"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-debug/zipball/e4b28a1d295fc977d8399db544336dd5b2764397", "reference": "e4b28a1d295fc977d8399db544336dd5b2764397", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4", "yiisoft/yii2": "~2.0.13"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34", "yiisoft/yii2-coding-standards": "~2.0", "yiisoft/yii2-swiftmailer": "*"}, "type": "yii2-extension", "extra": {"patches": {"phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch", "Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php81.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}}, "branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true}, "autoload": {"psr-4": {"yii\\debug\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The debugger extension for the Yii framework", "keywords": ["debug", "debugger", "dev", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-debug/issues", "source": "https://github.com/yiisoft/yii2-debug", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-debug", "type": "tidelift"}], "time": "2025-02-13T21:27:29+00:00"}, {"name": "yiisoft/yii2-queue", "version": "2.3.7", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-queue.git", "reference": "dbc9d4a7b2a6995cd19c3e334227482ef55e559b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-queue/zipball/dbc9d4a7b2a6995cd19c3e334227482ef55e559b", "reference": "dbc9d4a7b2a6995cd19c3e334227482ef55e559b", "shasum": ""}, "require": {"php": ">=5.5.0", "symfony/process": "^3.3||^4.0||^5.0||^6.0||^7.0", "yiisoft/yii2": "~2.0.14"}, "require-dev": {"aws/aws-sdk-php": ">=2.4", "cweagans/composer-patches": "^1.7", "enqueue/amqp-lib": "^0.8||^0.9.10||^0.10.0", "enqueue/stomp": "^0.8.39||^0.10.0", "opis/closure": "*", "pda/pheanstalk": "~3.2.1", "php-amqplib/php-amqplib": "^2.8.0||^3.0.0", "phpunit/phpunit": "4.8.34", "yiisoft/yii2-debug": "~2.1.0", "yiisoft/yii2-gii": "~2.2.0", "yiisoft/yii2-redis": "~2.0.0"}, "suggest": {"aws/aws-sdk-php": "Need for aws SQS.", "enqueue/amqp-lib": "Need for AMQP interop queue.", "enqueue/stomp": "Need for Stomp queue.", "ext-gearman": "Need for Gearman queue.", "ext-pcntl": "Need for process signals.", "pda/pheanstalk": "Need for Beanstalk queue.", "php-amqplib/php-amqplib": "Need for AMQP queue.", "yiisoft/yii2-redis": "Need for Redis queue."}, "type": "yii2-extension", "extra": {"patches": {"phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}}, "branch-alias": {"dev-master": "2.x-dev"}, "composer-exit-on-patch-failure": true}, "autoload": {"psr-4": {"yii\\queue\\": "src", "yii\\queue\\db\\": "src/drivers/db", "yii\\queue\\sqs\\": "src/drivers/sqs", "yii\\queue\\amqp\\": "src/drivers/amqp", "yii\\queue\\file\\": "src/drivers/file", "yii\\queue\\sync\\": "src/drivers/sync", "yii\\queue\\redis\\": "src/drivers/redis", "yii\\queue\\stomp\\": "src/drivers/stomp", "yii\\queue\\gearman\\": "src/drivers/gearman", "yii\\queue\\beanstalk\\": "src/drivers/beanstalk", "yii\\queue\\amqp_interop\\": "src/drivers/amqp_interop"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "z<PERSON>av<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Yii2 Queue Extension which supported DB, Redis, RabbitMQ, Beanstalk, SQS and Gearman", "keywords": ["async", "beanstalk", "db", "gearman", "gii", "queue", "rabbitmq", "redis", "sqs", "yii"], "support": {"docs": "https://github.com/yiisoft/yii2-queue/blob/master/docs/guide", "issues": "https://github.com/yiisoft/yii2-queue/issues", "source": "https://github.com/yiisoft/yii2-queue"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-queue", "type": "tidelift"}], "time": "2024-04-29T09:40:52+00:00"}, {"name": "yiisoft/yii2-symfonymailer", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-symfonymailer.git", "reference": "21f407239c51fc6d50d369e4469d006afa8c9b2c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-symfonymailer/zipball/21f407239c51fc6d50d369e4469d006afa8c9b2c", "reference": "21f407239c51fc6d50d369e4469d006afa8c9b2c", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "1.0.0", "symfony/mailer": "^6.4 || ^7.0", "symfony/mime": "^6.4 || ^7.0", "yiisoft/yii2": ">=2.0.4"}, "require-dev": {"maglnet/composer-require-checker": "^4.7", "phpunit/phpunit": "^10.5", "roave/infection-static-analysis-plugin": "^1.34", "symplify/easy-coding-standard": "^12.1", "vimeo/psalm": "^5.20"}, "suggest": {"yiisoft/yii2-psr-log-source": "Allows routing transport logs to your Yii2 logger"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}, "sort-packages": true}, "autoload": {"psr-4": {"yii\\symfonymailer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The SymfonyMailer integration for the Yii framework", "keywords": ["email", "mail", "mailer", "symfony", "symfonymailer", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-symfonymailer/issues", "source": "https://github.com/yiisoft/yii2-symfonymailer", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-symfonymailer", "type": "tidelift"}], "time": "2024-01-29T14:13:45+00:00"}], "packages-dev": [{"name": "craftcms/generator", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/craftcms/generator.git", "reference": "4ccc62474698905c252c3ddb3352c0a63fae9765"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/generator/zipball/4ccc62474698905c252c3ddb3352c0a63fae9765", "reference": "4ccc62474698905c252c3ddb3352c0a63fae9765", "shasum": ""}, "require": {"craftcms/cms": "^5.0.0-beta.1", "nette/php-generator": "^4.0", "nikic/php-parser": "^4.15", "php": "^8.2"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main", "pestphp/pest": "^1.22"}, "type": "yii2-extension", "extra": {"bootstrap": "craft\\generator\\Extension"}, "autoload": {"psr-4": {"craft\\generator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["mit"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Craft CMS component generator", "homepage": "https://craftcms.com", "keywords": ["cms", "craftcms", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/generator/issues?state=open", "rss": "https://github.com/craftcms/generator/releases.atom", "source": "https://github.com/craftcms/generator"}, "time": "2024-06-19T14:43:28+00:00"}, {"name": "nette/php-generator", "version": "v4.1.8", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "42806049a7774a2bd316c958f5dcf01c6b5c56fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/42806049a7774a2bd316c958f5dcf01c6b5c56fa", "reference": "42806049a7774a2bd316c958f5dcf01c6b5c56fa", "shasum": ""}, "require": {"nette/utils": "^3.2.9 || ^4.0", "php": "8.0 - 8.4"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.4", "nikic/php-parser": "^4.18 || ^5.0", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"nikic/php-parser": "to use ClassType::from(withBodies: true) & ClassType::fromCode()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 8.4 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "support": {"issues": "https://github.com/nette/php-generator/issues", "source": "https://github.com/nette/php-generator/tree/v4.1.8"}, "time": "2025-03-31T00:29:29+00:00"}, {"name": "nette/utils", "version": "v4.0.6", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "ce708655043c7050eb050df361c5e313cf708309"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/ce708655043c7050eb050df361c5e313cf708309", "reference": "ce708655043c7050eb050df361c5e313cf708309", "shasum": ""}, "require": {"php": "8.0 - 8.4"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.6"}, "time": "2025-03-30T21:06:30+00:00"}, {"name": "nikic/php-parser", "version": "v4.19.4", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/715f4d25e225bc47b293a8b997fe6ce99bf987d2", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.19.4"}, "time": "2024-09-29T15:01:53+00:00"}, {"name": "psy/psysh", "version": "v0.12.8", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "85057ceedee50c49d4f6ecaff73ee96adb3b3625"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/85057ceedee50c49d4f6ecaff73ee96adb3b3625", "reference": "85057ceedee50c49d4f6ecaff73ee96adb3b3625", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^5.0 || ^4.0", "php": "^8.0 || ^7.4", "symfony/console": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well."}, "bin": ["bin/psysh"], "type": "library", "extra": {"bamarni-bin": {"bin-links": false, "forward-command": false}, "branch-alias": {"dev-main": "0.12.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.8"}, "time": "2025-03-16T03:05:19+00:00"}, {"name": "symfony/console", "version": "v7.2.6", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "0e2e3f38c192e93e622e41ec37f4ca70cfedf218"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/0e2e3f38c192e93e622e41ec37f4ca70cfedf218", "reference": "0e2e3f38c192e93e622e41ec37f4ca70cfedf218", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^6.4|^7.0"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v7.2.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-07T19:09:28+00:00"}, {"name": "yiisoft/yii2-shell", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-shell.git", "reference": "c0aef8874eb6e9e6a56cf2972e422457008f9e18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-shell/zipball/c0aef8874eb6e9e6a56cf2972e422457008f9e18", "reference": "c0aef8874eb6e9e6a56cf2972e422457008f9e18", "shasum": ""}, "require": {"psy/psysh": "~0.9.3|~0.10.3|^0.11.0|^0.12.0", "symfony/var-dumper": "~2.7|~3.0|~4.0|~5.0", "yiisoft/yii2": "~2.0.0"}, "type": "yii2-extension", "extra": {"bootstrap": "yii\\shell\\Bootstrap", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\shell\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "svku<PERSON><PERSON>@gmail.com"}], "description": "The interactive shell extension for Yii framework", "keywords": ["shell", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-shell/issues", "source": "https://github.com/yiisoft/yii2-shell", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://opencollective.com/yiisoft", "type": "open_collective"}], "time": "2025-02-13T21:05:12+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": [], "prefer-stable": true, "prefer-lowest": false, "platform": [], "platform-dev": [], "platform-overrides": {"php": "8.2"}, "plugin-api-version": "2.3.0"}