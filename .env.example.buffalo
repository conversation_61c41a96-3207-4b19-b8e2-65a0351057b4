# Read about configuration, here:
# https://craftcms.com/docs/5.x/configure.html

# The application ID used to to uniquely store session and cache data, mutex locks, and more
CRAFT_APP_ID=

# The environment Craft is currently running in (dev, staging, production, etc.)
CRAFT_ENVIRONMENT=dev

# Database connection settings
CRAFT_DB_DRIVER=mysql
CRAFT_DB_SERVER=mysql
CRAFT_DB_PORT=3306
CRAFT_DB_DATABASE=craft
CRAFT_DB_USER=root
CRAFT_DB_PASSWORD=craft
CRAFT_DB_SCHEMA=
CRAFT_DB_TABLE_PREFIX=
CRAFT_DB_CHARSET=utf8mb4
CRAFT_DB_COLLATION=utf8mb4_unicode_ci

# General settings
CRAFT_SECURITY_KEY=
CRAFT_DEV_MODE=true
CRAFT_ALLOW_ADMIN_CHANGES=true
CRAFT_DISALLOW_ROBOTS=true

PRIMARY_SITE_URL=http://localhost/
