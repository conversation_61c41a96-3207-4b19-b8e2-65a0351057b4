<!DOCTYPE html>
<html lang="en" class="js">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layered Scroll Demo - Botanical Confetti</title>
    <style>
        /* Inline critical styles for demo */
        :root {
          --white: #fff;
          --black: #000;
          --linen: #FFFAF4;
          --linen-dark: #F5ECE1;
          --gold: #D7A461;
          --gold-light: #F8E0C0;
          --slate: #262322;
          --grey: #514E4D;
          --font-display: "Georgia", serif;
          --font-body: "Arial", sans-serif;
          --easeOutQuart: cubic-bezier(0.25, 1, 0.5, 1);
          --container-padding: max(5vw, 1.25rem);
          --global-width: 1440px;
        }
        
        * { box-sizing: border-box; }
        
        body {
          margin: 0;
          font-family: var(--font-body);
          background-color: var(--linen);
          color: var(--slate);
          line-height: 1.5;
        }
        
        .container {
          max-width: var(--global-width);
          margin-left: auto;
          margin-right: auto;
          padding-left: var(--container-padding);
          padding-right: var(--container-padding);
        }
        
        /* Component styles */
        .layered-scroll {
          position: relative;
          background-color: var(--linen);
        }

        .layered-scroll.is-enabled {
          /* Additional padding will be added via JS */
        }

        .layered-scroll__section {
          position: relative;
          min-height: 100vh;
          display: flex;
          align-items: center;
          background-color: var(--linen);
          border-radius: 0;
          overflow: hidden;
          z-index: 1;
        }
        
        .layered-scroll__section:nth-child(even) {
          background-color: var(--linen-dark);
        }
        
        .layered-scroll.is-enabled .layered-scroll__section {
          position: sticky;
          top: 0;
          border-radius: 0 0 24px 24px;
        }

        .layered-scroll__content {
          max-width: var(--global-width);
          margin-left: auto;
          margin-right: auto;
          padding-left: var(--container-padding);
          padding-right: var(--container-padding);
          display: grid;
          grid-template-columns: 1fr;
          gap: 32px;
          align-items: center;
          width: 100%;
          padding-block: 80px;
        }
        
        @media (min-width: 768px) {
          .layered-scroll__content {
            grid-template-columns: 1fr 1fr;
            gap: 64px;
            padding-block: 120px;
          }
          
          .layered-scroll.is-enabled .layered-scroll__section {
            border-radius: 0 0 48px 48px;
          }
        }

        .layered-scroll__image {
          position: relative;
          aspect-ratio: 4/3;
          border-radius: 12px;
          overflow: hidden;
        }
        
        @media (min-width: 768px) {
          .layered-scroll__image {
            aspect-ratio: 3/4;
          }
        }
        
        .layered-scroll__image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
        }

        .layered-scroll__text {
          display: flex;
          flex-direction: column;
          gap: 24px;
        }

        .layered-scroll__title {
          font-family: var(--font-display);
          font-size: clamp(24px, 4vw, 32px);
          font-weight: 400;
          color: var(--slate);
          margin: 0;
          letter-spacing: 0.1em;
        }

        .layered-scroll__description {
          font-family: var(--font-body);
          font-size: clamp(18px, 2.5vw, 24px);
          line-height: 1.6;
          font-weight: 400;
          color: var(--grey);
          margin: 0;
        }

        /* Alternating layout */
        @media (min-width: 768px) {
          .layered-scroll__section:nth-child(even) .layered-scroll__content .layered-scroll__image {
            order: 2;
          }
          
          .layered-scroll__section:nth-child(even) .layered-scroll__content .layered-scroll__text {
            order: 1;
          }
        }

        /* Animation states */
        .layered-scroll__section .layered-scroll__image,
        .layered-scroll__section .layered-scroll__text {
          transition: 
            opacity 0.8s var(--easeOutQuart),
            transform 0.8s var(--easeOutQuart);
        }
        
        .layered-scroll__section:not(.is-visible) .layered-scroll__image {
          opacity: 0;
          transform: translateY(40px);
        }
        
        .layered-scroll__section:not(.is-visible) .layered-scroll__text {
          opacity: 0;
          transform: translateY(20px);
        }

        .layered-scroll__overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.1) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
          pointer-events: none;
          z-index: 2;
        }
    </style>
</head>
<body>
    <div class="layered-scroll" data-controller="layered-scroll">
        
        <!-- Section 1: Planting and Growing -->
        <section class="layered-scroll__section" data-layered-scroll-target="section">
            <div class="layered-scroll__overlay" data-layered-scroll-target="overlay"></div>
            <div class="layered-scroll__content">
                <div class="layered-scroll__image">
                    <img src="https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=800&h=1000&fit=crop" 
                         alt="Hands planting flowers in soil" 
                         width="800" 
                         height="1000">
                </div>
                <div class="layered-scroll__text">
                    <h2 class="layered-scroll__title">Planting and Growing</h2>
                    <p class="layered-scroll__description">
                        Our petals begin on the terraces of Poon Petal, Bengali in-grown 
                        National Park, and have been partnered with over 40 local farmers 
                        meaning us. Our biggest bougainvillea farm in this section also don't just 
                        as a participant in a worldwide, just-and-delightful rain and sun!
                    </p>
                </div>
            </div>
        </section>

        <!-- Section 2: Community and Conservation -->
        <section class="layered-scroll__section" data-layered-scroll-target="section">
            <div class="layered-scroll__overlay" data-layered-scroll-target="overlay"></div>
            <div class="layered-scroll__content">
                <div class="layered-scroll__image">
                    <img src="https://images.unsplash.com/photo-1564760055775-d63b17a55c44?w=800&h=1000&fit=crop" 
                         alt="Elephants in their natural habitat" 
                         width="800" 
                         height="1000">
                </div>
                <div class="layered-scroll__text">
                    <h2 class="layered-scroll__title">Community and Conservation</h2>
                    <p class="layered-scroll__description">
                        Our team and elephants from the greatest communities bordering 
                        Poon Petal, and we are proud to be a part of their daily lives. 
                        Proceeds from the petals you purchase go towards supporting 
                        these communities, conserving habitats, and protecting elephants 
                        and all others.
                    </p>
                </div>
            </div>
        </section>

        <!-- Section 3: Harvesting -->
        <section class="layered-scroll__section" data-layered-scroll-target="section">
            <div class="layered-scroll__overlay" data-layered-scroll-target="overlay"></div>
            <div class="layered-scroll__content">
                <div class="layered-scroll__image">
                    <img src="https://images.unsplash.com/photo-1595147389795-37094173bfd8?w=800&h=1000&fit=crop" 
                         alt="Person harvesting colorful flower petals" 
                         width="800" 
                         height="1000">
                </div>
                <div class="layered-scroll__text">
                    <h2 class="layered-scroll__title">Harvesting</h2>
                    <p class="layered-scroll__description">
                        Our petals are handpicked and harvested at the perfect time to 
                        ensure the very best quality and flower content.
                    </p>
                </div>
            </div>
        </section>

        <!-- Additional sections would continue here... -->

    </div>

    <script type="module">
        // Simplified controller for demo
        class LayeredScrollController {
            constructor(element) {
                this.element = element
                this.sections = Array.from(element.querySelectorAll('[data-layered-scroll-target="section"]'))
                this.overlays = Array.from(element.querySelectorAll('[data-layered-scroll-target="overlay"]'))
                this.gap = 24
                this.isEnabled = false
                
                this.init()
            }
            
            init() {
                this.resize = this.resize.bind(this)
                this.scroll = this.scroll.bind(this)
                
                window.addEventListener('resize', this.resize)
                this.initVisibilityObserver()
                this.resize()
            }
            
            initVisibilityObserver() {
                this.observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('is-visible')
                        }
                    })
                }, { threshold: 0.2, rootMargin: '-10% 0px' })
                
                this.sections.forEach(section => this.observer.observe(section))
            }
            
            enable() {
                if (this.isEnabled) return
                this.isEnabled = true
                
                this.element.classList.add('is-enabled')
                this.element.style.paddingBottom = `${this.gap * (this.sections.length - 1)}px`
                window.addEventListener('scroll', this.scroll, { passive: true })
                this.scroll()
            }
            
            disable() {
                if (!this.isEnabled) return
                this.isEnabled = false
                
                this.element.classList.remove('is-enabled')
                this.element.style.paddingBottom = ''
                this.sections.forEach(section => {
                    section.style.transform = ''
                    section.style.zIndex = ''
                })
                this.overlays.forEach(overlay => overlay.style.opacity = '')
                window.removeEventListener('scroll', this.scroll)
            }
            
            resize() {
                const sectionHeight = Math.max(...this.sections.map(s => s.getBoundingClientRect().height))
                
                if (sectionHeight < window.innerHeight * 0.6) {
                    this.disable()
                    return
                } else {
                    this.enable()
                }
                
                this.updateSectionData()
            }
            
            updateSectionData() {
                const containerTop = this.element.getBoundingClientRect().top + window.scrollY
                const sectionHeight = this.sections[0]?.getBoundingClientRect().height || window.innerHeight
                
                this.sectionData = this.sections.map((section, index) => ({
                    element: section,
                    start: containerTop + (sectionHeight * index),
                    end: containerTop + (sectionHeight * (index + 1))
                }))
            }
            
            scroll() {
                if (!this.isEnabled || !this.sectionData) return
                
                const scrollY = window.scrollY
                
                this.sectionData.forEach((section, index) => {
                    const isLast = index === this.sections.length - 1
                    
                    if (isLast) {
                        section.element.style.transform = `translateY(${this.gap * index}px)`
                        section.element.style.zIndex = this.sections.length - index
                    } else {
                        const progress = Math.max(0, Math.min(1, 
                            (scrollY - section.start) / (section.end - section.start)
                        ))
                        
                        section.element.style.transform = `translateY(${this.gap * index}px)`
                        section.element.style.zIndex = this.sections.length - index
                        
                        if (this.overlays[index]) {
                            const opacity = Math.max(0, Math.min(0.3, (progress - 0.5) * 0.6))
                            this.overlays[index].style.opacity = opacity
                        }
                    }
                })
            }
        }
        
        // Initialize the component
        document.addEventListener('DOMContentLoaded', () => {
            const element = document.querySelector('[data-controller="layered-scroll"]')
            if (element) {
                new LayeredScrollController(element)
            }
        })
    </script>
</body>
</html>
