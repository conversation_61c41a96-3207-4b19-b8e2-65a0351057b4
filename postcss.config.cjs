module.exports = {
  plugins: [
    require('postcss-import-ext-glob'),
    require('postcss-import'),
    require('postcss-mixins')({ mixinsFiles: './src/styles/mixins/**/*.css' }),
    require('@csstools/postcss-global-data')({ files: ['./src/styles/breakpoints.css'] }),
    require('postcss-nested'),
    require('postcss-custom-media'),
    require('postcss-utopia')({
      minWidth: 385,
      maxWidth: 1440,
    }),
    require('postcss-preset-env'),
  ],
}
