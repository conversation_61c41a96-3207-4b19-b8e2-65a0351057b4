{# 
  Layered Scroll Component Template
  
  Usage:
  {% include 'site/components/layered-scroll.twig' with {
    sections: [
      {
        title: 'Section Title',
        description: 'Section description text...',
        image: imageAsset,
        imageAlt: 'Alt text for image'
      },
      // ... more sections
    ]
  } %}
#}

{% set sections = sections ?? [] %}

{% if sections|length > 0 %}
<div class="layered-scroll" data-controller="layered-scroll" data-visible-target="item">
  {% for section in sections %}
    <section class="layered-scroll__section animate-up" 
             data-layered-scroll-target="section" 
             data-visible-target="item">
      <div class="layered-scroll__overlay" data-layered-scroll-target="overlay"></div>
      
      <div class="layered-scroll__content">
        {% if section.image %}
          <div class="layered-scroll__image">
            {% set image = section.image %}
            {% if image.kind == 'image' %}
              <img src="{{ image.getUrl({width: 800, format: 'webp'}) }}" 
                   alt="{{ section.imageAlt ?? image.alt ?? section.title }}" 
                   width="{{ image.width }}" 
                   height="{{ image.height }}">
            {% endif %}
          </div>
        {% endif %}
        
        <div class="layered-scroll__text">
          {% if section.title %}
            <h2 class="layered-scroll__title">{{ section.title }}</h2>
          {% endif %}
          
          {% if section.description %}
            {% if section.description is iterable %}
              {% for paragraph in section.description %}
                <p class="layered-scroll__description">{{ paragraph }}</p>
              {% endfor %}
            {% else %}
              <div class="layered-scroll__description rich-text">
                {{ section.description|raw }}
              </div>
            {% endif %}
          {% endif %}
        </div>
      </div>
    </section>
  {% endfor %}
</div>
{% endif %}
