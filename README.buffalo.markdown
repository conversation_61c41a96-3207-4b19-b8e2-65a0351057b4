# For brand new projects

This project contains an empty composer.lock file. In order to generate one in a reliable way you'll need to build your console image:

```
docker compose build console
```

Then you need to get the composer.lock file out of that image. This works because we mount composer.lock into the container for `run` but not for `build`

```
docker compose run --rm console composer update
```

Then build your entire project using your new lockfile:

```
docker compose build
```

And carry on as normal with your setup (MariaDB should come up before you run this command):

```
docker compose run --rm console php craft setup/welcome
```

Then once everything's done here, running:

```
docker compose up
```

Should get you to where you need to be.

Because of this switch to using <PERSON>'s docker images as bases, commands that you used to run in the app container should now be run in the console container.
