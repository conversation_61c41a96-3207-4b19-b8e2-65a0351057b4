import { defineConfig, mergeConfig } from 'vite'
import ViteRestart from 'vite-plugin-restart'
import StimulusHMR from 'vite-plugin-stimulus-hmr'
import baseConfig from './vite.config'

export default mergeConfig(
  baseConfig,
  defineConfig({
    base: '',
    server: {
      host: '0.0.0.0',
      strictPort: true,
    },
    css: {
      devSourcemap: true,
    },
    plugins: [
      StimulusHMR(),
      ViteRestart({
        reload: ['templates/**/*'],
      }),
    ],
  }),
)
