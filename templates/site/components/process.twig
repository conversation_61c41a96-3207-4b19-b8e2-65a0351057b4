<div class="section theme--dark-linen">
	<div class="process container">
		<div class="process__header animate-fade" data-visible-target="item">
			<h2>{{item.title}}</h2>
			<div class="rich-text">
				{{item.richTextSimple}}
			</div>
		</div>

		<div class="process__list" data-controller="tiles" style="grid-template-rows: repeat({{ item.steps|length }}, 1fr);" data-gap="95" data-visible-target="item">
			{% for process in item.steps.with([['image', { withTransforms: [{ width: 1000, format: 'webp' }] }]]).all() %}
				<div class="process__item" data-tiles-target="tile">
					<span class="rule"></span>

					<div class="process__gallery">
						{% if process.image %}
							{% set image = process.image.one() %}
							<figure>
								<img src="{{image.getUrl({width: 1000, format: 'webp'})}}" alt="{{image.alt}}" width="{{image.width}}" height="{{image.height}}">
							</figure>
						{% endif %}
					</div>

					<div class="process__content">
						<h3>{{process.title}}</h3>

						{% if process.description|length > 0 %}
							<div class="process__desc rich-text">
								{{process.description}}
							</div>
						{% endif %}

						{% if process.footer|length > 0 %}
							<div class="process__footer rich-text">
								{{process.footer}}
							</div>
						{% endif %}
					</div>
				</div>
			{% endfor %}
		</div>
	</div>
</div>