<div class="section theme--dark-linen">
	<div class="gallery">
		<div class="gallery__decor">
			<img src="/static/images/leaves-gallery.png" alt="">
		</div>

		<div class="gallery__inner">
			<div class="container">
				<div class="gallery__header animate-fade" data-visible-target="item">
					<span class="rule"></span>

					<h1>{{item.title}}</h1>

					{% if item.richTextSimple %}
						<div class="rich-text">
							{{item.richTextSimple}}
						</div>
					{% endif %}
				</div>

				<div class="slider animate-fade" data-visible-target="item" data-controller="slider" data-slider-options='{"centeredSlides": true, "loop": true}'>
					<div class="swiper-container" data-slider-target="slides">
						<div class="swiper-wrapper">
							{% set images = item.images.withTransforms([{width: 1000, format: 'webp'}]) %}
							{% for image in images %}
								<div class="swiper-slide">
									<div class="slider__slide">
										<figure>
											<img src="{{image.getUrl({width: 1000, format: 'webp'})}}" alt="{{image.alt}}" width="{{image.width}}" height="{{image.height}}">
										</figure>
									</div>
								</div>
							{% endfor %}
							{% for image in images %}
								<div class="swiper-slide">
									<div class="slider__slide">
										<figure>
											<img src="{{image.getUrl({width: 1000, format: 'webp'})}}" alt="{{image.alt}}" width="{{image.width}}" height="{{image.height}}">
										</figure>
									</div>
								</div>
							{% endfor %}
						</div>
					</div>

					{% if item.images|length > 1 %}
						<div class="slider__controls-fixed">
							<button class="slider__control slider__control--previous" data-slider-target="previous" type="button" aria-label="Previous slide" disabled>
								{{ svg('@webroot/static/svg/arrow-left.svg') }}
							</button>
							<button class="slider__control slider__control--next" data-slider-target="next" type="button" aria-label="Next slide">
								{{ svg('@webroot/static/svg/arrow-right.svg') }}
							</button>
						</div>
					{% endif %}
				</div>
			</div>
		</div>
	</div>
</div>