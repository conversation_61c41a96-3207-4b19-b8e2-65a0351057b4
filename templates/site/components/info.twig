<div class="section {% if item.background %}theme--{{item.background}}{% endif %}">
	<div class="info">
		<div class="info__inner container">
			<div class="info__header animate-fade" data-visible-target="item">
				<h2>{{item.title}}</h2>
				
				{% if item.image|length %}
					{% set image = item.image.one() %}
					<figure>
						<img src="{{image.getUrl({width: 900, format: 'webp'})}}" alt="{{image.alt}}" width="{{image.width}}" height="{{image.height}}">
					</figure>
				{% endif %}
			</div>

			<div class="info__grid">
				{% for info in item.blocks.with([['image', { withTransforms: [{ width: 900, format: 'webp' }] }]]) %}
					<div class="info__item animate-up" data-visible-target="item">
						{% if info.image %}
							{% set image = info.image.one() %}
							<figure>
								<img src="{{image.getUrl({width: 900, format: 'webp'})}}" alt="{{image.alt}}" width="{{image.width}}" height="{{image.height}}">
							</figure>
							
							<div class="info__item__text animate-left" data-visible-target="item">
								<h3>{{info.title}}</h3>
								<div class="rich-text">
									{{info.richTextSimple}}
								</div>
							</div>
						{% endif %}
					</div>
				{% endfor %}
			</div>
			
			<div class="info__footer animate-fade" data-visible-target="item">
				{{item.richTextSimple}}
			</div>
		</div>
	</div>
</div>