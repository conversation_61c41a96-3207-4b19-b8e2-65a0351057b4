<div class="section theme--dark-linen">
	<div class="petals {% if item.decor %}petals--decor{% endif %}">
		<div class="petals__inner container">
			<div class="petals__header animate-fade" data-visible-target="item">
				{% if item.decor %}
					{{ svg('@webroot/static/svg/diamond.svg') }}
				{% endif %}
				
				<span class="rule"></span>
				
				{% if index == 1 %}
					<h1>{{item.title}}</h1>
				{% else %}
					<h2>{{item.title}}</h2>
				{% endif %}
				
				<span class="rule"></span>
				
				{% if item.richTextSimple %}
					<div class="rich-text">
						{{item.richTextSimple}}
					</div>
				{% endif %}
				
				{% if item.image|length %}
					{% set image = item.image.one() %}
					<figure>
						<img src="{{image.getUrl({width: 900, format: 'webp'})}}" alt="{{image.alt}}" width="{{image.width}}" height="{{image.height}}">
					</figure>
				{% endif %}
			</div>

			<div class="petals__grid">
				{% for petal in item.products.with([['image', { withTransforms: [{ width: 900, format: 'webp' }] }]]) %}
					<div class="petals__item animate-up" data-visible-target="item">
						{% if petal.image %}
							{% set image = petal.image.one() %}
							<figure>
								<img src="{{image.getUrl({width: 900, format: 'webp'})}}" alt="{{image.alt}}" width="{{image.width}}" height="{{image.height}}">
							</figure>
						{% endif %}

						<a {% if item.slug == 'our-petals' %}data-controller="scroll-to" data-action="click->scroll-to#scroll" {% endif %} href="/our-petals#{{petal.slug}}">{{petal.title}}</a>
					</div>
				{% endfor %}
			</div>
			
			{% if item.links|length %}
				<div class="petals__links animate-fade" data-visible-target="item">
					{% for link in item.links.all() %}
						<a {% if link.linkUrl.target %}target="_blank"{% endif %} href="{{link.linkUrl}}" class="button {% if loop.index is even %}button--secondary{% endif %}">
							{{link.title}}
							{{ svg('@webroot/static/svg/arrow.svg') }}
						</a>
					{% endfor %}
				</div>
			{% endif %}

			{% if item.decor %}
				<div class="petals__decor">
					<span class="rule"></span>
					{{ svg('@webroot/static/svg/diamond.svg') }}
					<span class="rule"></span>
				</div>
			{% endif %}
		</div>
	</div>
</div>