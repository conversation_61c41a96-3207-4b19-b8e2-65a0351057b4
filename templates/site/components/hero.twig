<div class="section theme--dark-linen">
	<div class="hero">
		<div class="hero__inner container">
			<div class="hero__header">
				<div class="hero__decor">
					<img src="/static/images/leaves-hero.png" alt="">
				</div>

				<div class="hero__heading ruled-header animate-up" data-visible-target="item">
					{% if index == 1 %}
						<h1>
							<span class="rule"></span>
							<span>{{item.title}}</span>
							<span class="rule"></span>
						</h1>
					{% else %}
						<h2>
							<span class="rule"></span>
							<span>{{item.title}}</span>
							<span class="rule"></span>
						</h2>
					{% endif %}
				</div>

				<div class="rich-text animate-fade" data-visible-target="item">
					{{item.richTextSimple}}
				</div>
			</div>

			{% if item.image %}
				{% set image = item.image.withTransforms([{width: 2000, format: 'webp'}]).one() %}
				<figure class="animate-fade" data-visible-target="item">
					<img src="{{image.getUrl({width: 2000, format: 'webp'})}}" alt="{{image.alt}}" width="{{image.width}}" height="{{image.height}}">
				</figure>
			{% endif %}
		</div>
	</div>
</div>