<div class="section">
	<div class="enquiries">
		<div class="enquiries__header">
			<img src="/static/images/leaves-enquiries-left.png" alt="">
			<img src="/static/images/leaves-enquiries-right.png" alt="">

			<div class="enquiries__heading ruled-header animate-fade" data-visible-target="item">
				{% if index == 1 %}
					<h1>
						<span class="rule"></span>
						<span>{{item.title}}</span>
						<span class="rule"></span>
					</h1>
				{% else %}
					<h2>
						<span class="rule"></span>
						<span>{{item.title}}</span>
						<span class="rule"></span>
					</h2>
				{% endif %}
			</div>

			<div class="rich-text rich-text--center animate-fade" data-visible-target="item">
				{{item.intro}}
			</div>
		</div>

		<div class="enquiries__inner container">
			<div class="enquiries__orders animate-fade" data-visible-target="item" data-controller="tabs">
				<div class="enquiries__orders__header">
					<p>Select option</p>
				</div>

				<div class="enquiries__orders__controls">
					<div class="enquiries__orders__controls__inner">
						<button data-tabs-target="link" data-action="tabs#select" class="button  button--secondary enquiries__orders__control is-selected">Small orders</button>
						<button data-tabs-target="link" data-action="tabs#select" class="button  button--secondary enquiries__orders__control">Bulk orders</button>
					</div>
				</div>

				<div class="enquiries__orders__contents">
					<div data-tabs-target="content" class="enquiries__orders__content is-selected">
						<div class="rich-text">
							{{item.smallOrdersText}}
						</div>

						{% for office in item.offices.with([['image', { withTransforms: [{ width: 600, format: 'webp' }] }]]).all() %}
							<div class="enquiries__orders__item">
								<div class="enquiries__orders__image">
									{% if office.image|length > 0 %}
										{% set image = office.image.one() %}

										<figure>
											<img src="{{image.getUrl({width: 600, format: 'webp'})}}" alt="{{image.alt}}" width="{{image.width}}" height="{{image.height}}">
										</figure>
									{% endif %}
								</div>

								<div class="enquiries__orders__text">
									<ul>
										{% for link in office.links.all() %}
											<li><a {% if link.linkUrl.target %}target="_blank"{% endif %} href="{{link.linkUrl}}">{{link.title}}</a></li>
										{% endfor %}
									</ul>
								</div>
							</div>
						{% endfor %}
					</div>

					<div data-tabs-target="content" class="enquiries__orders__content">
						<div class="rich-text">
							{{item.bulkOrdersText}}
						</div>

						{% set form = craft.freeform.form("contact", {returnUrl: craft.app.request.absoluteUrl ~ '?submitted=true',})%}
						{% if form %}
							<div class="enquiries__orders__form">{{ form.render() }}</div>
						{% endif %}
					</div>
				</div>
			</div>
			
			<div class="enquiries__faq" data-controller="accordion" id="faq">
				<div class="enquiries__faq__header animate-fade" data-visible-target="item">
					<h2>Order faq</h2>
				</div>
				
				{% for faq in item.faq %}
					<div class="enquiries__faq__item animate-fade" data-visible-target="item">
						<button class="enquiries__faq__item__trigger" data-accordion-target="trigger" data-action="click->accordion#toggle">
							{{faq.title}}
			
							<span>
								{{ svg('@webroot/static/svg/arrow-down.svg') }}
							</span>
						</button>
			
						<div class="enquiries__faq__item__content" data-accordion-target="content">
							<div class="enquiries__faq__item__content__inner">
								{{faq.richText}}
							</div>
						</div>
					</div>
				{% endfor %}
			</div>
		</div>
	</div>
</div>