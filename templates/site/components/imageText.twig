<div class="section theme--dark-linen">
	<div class="image-text">
		<div class="image-text__decor">
			<img src="/static/images/leaves-image-text.png" alt="">
		</div>

		<div class="image-text__header animate-fade" data-visible-target="item">
			<div class="rich-text">
				{{item.richTextSimple}}
				<span class="rule"></span>
			</div>
		</div>

		<div class="image-text__inner container">
			{% for row in item.blocksContent.with([['image', { withTransforms: [{ width: 900, format: 'webp' }] }]]).all() %}
				<div class="image-text__item">
					<div class="image-text__image animate-fade" data-visible-target="item">
						{% if row.image|length > 0 %}
							{% set image = row.image.one() %}

							<figure>
								<img src="{{image.getUrl({width: 900, format: 'webp'})}}" alt="{{image.alt}}" width="{{image.width}}" height="{{image.height}}">
							</figure>
						{% endif %}
					</div>

					<div class="image-text__text rich-text animate-up" data-visible-target="item">
						{{row.richText}}
					</div>
				</div>
			{% endfor %}
		</div>
	</div>
</div>