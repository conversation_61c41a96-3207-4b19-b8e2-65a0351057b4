<div class="section theme--dark-linen">
	<div class="products container">
		<div class="products__list" data-controller="tiles" style="grid-template-rows: repeat({{ item.steps|length }}, 1fr);" data-gap="0" data-visible-target="item">
			{% for product in item.products.with([['images', { withTransforms: [{ width: 1000, format: 'webp' }] }]]).all() %}
				<div class="products__item" data-tiles-target="tile" id="{{product.slug}}">
					<div class="products__gallery">
						<div class="slider animate-fade" data-visible-target="item" data-controller="slider">
							<div class="swiper-container" data-slider-target="slides">
								<div class="swiper-wrapper">
									{% for image in product.images %}
										<div class="swiper-slide">
											<div class="slider__slide">
												<figure>
													<img src="{{image.getUrl({width: 1500, format: 'webp'})}}" alt="{{image.alt}}" width="{{image.width}}" height="{{image.height}}">
												</figure>
											</div>
										</div>
									{% endfor %}
								</div>
							</div>
			
							{% if product.images|length > 1 %}
								<div class="slider__controls">
									<button class="slider__control slider__control--previous" data-slider-target="previous" disabled type="button">
										{{ svg('@webroot/static/svg/arrow-left.svg') }}
									</button>

									<div class="slider__dots" data-slider-target="pagination"></div>

									<button class="slider__control slider__control--next" data-slider-target="next" disabled type="button">
										{{ svg('@webroot/static/svg/arrow-right.svg') }}
									</button>
								</div>
							{% endif %}
						</div>
					</div>

					<div class="products__content">
						<div class="products__content__inner">
							<h2>{{product.title}}</h2>
							
							<div class="products__sub">
								<p>{{product.subheading}}</p>
							
								<div class="products__tag">
									<p>{{product.tag}}</p>
									
									{% if product.tagHover|length > 0 %}
										<div class="products__tag__hover">
											{{product.tagHover}}
										</div>
									{% endif %}
								</div>
							</div>

							<div class="products__desc">
								{{product.description}}
							</div>

							<div class="products__available">
								<p class="products__available__title">Available in</p>
							
								<ul class="products__available__list">
									{% for item in product.options %}
										<li class="products__available__item">
											<button>{{item.title}}</button>
							
											<div class="products__available__hover">
												{{item.text}}
											</div>
										</li>
									{% endfor %}
								</ul>
							</div>

							{% if header.buttonLabel|length > 0 %}
								<a href="{{header.buttonLink}}" class="button button--secondary">
									{{header.buttonLabel}}
									{{ svg('@webroot/static/svg/arrow.svg') }}
								</a>
							{% endif %}
						</div>

						<div class="products__icons">
							<ul>
								<li>
									{{ svg('@webroot/static/svg/leaf-1.svg') }}
									<p>No staining</p>
								</li>

								<li>
									{{ svg('@webroot/static/svg/leaf-2.svg') }}
									<p>Scent free</p>
								</li>

								<li>
									{{ svg('@webroot/static/svg/leaf-3.svg') }}
									<p>No pesticides or chemicals</p>
								</li>

								<li>
									{{ svg('@webroot/static/svg/leaf-4.svg') }}
									<p>No artificial colours</p>
								</li>

								<li>
									{{ svg('@webroot/static/svg/leaf-5.svg') }}
									<p>Vegan</p>
								</li>
							</ul>
						</div>
					</div>
				</div>
			{% endfor %}
		</div>
	</div>
</div>