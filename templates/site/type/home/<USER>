{% set bodyClass = 'is-home' %}

{% extends "layouts/_application" %}

{% block content %}

	<div class="landing animate-fade" data-visible-target="item">
		<div class="landing__inner container">
			{% if entry.image %}
				{% set image = entry.image.one() %}
				<figure>
					<img src="{{image.getUrl({width: 1200, format: 'webp'})}}" alt="{{image.alt}}" width="{{image.width}}" height="{{image.height}}">
				</figure>
			{% endif %}

			<h1>{{entry.title}}</h1>
			
			<div class="rich-text">
				{{entry.richTextSimple}}
			</div>
			
			{% for item in entry.links.all() %}
				<a {% if item.linkUrl.target %}target="_blank"{% endif %} href="{{item.linkUrl}}" class="button">
					{{item.title}}
					{{ svg('@webroot/static/svg/arrow.svg') }}
				</a>
			{% endfor %}
		</div>
	</div>
	
{% endblock %}