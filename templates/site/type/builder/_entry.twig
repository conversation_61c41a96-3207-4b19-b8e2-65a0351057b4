{% extends "layouts/_application" %}

{% block content %}
	{% cache globally using key 'builder-'~entry.id %}
		{% for item in entry.builder.all() %}
			{% include "site/components/"~item.type with { index: loop.index } %}
		{% endfor %}
	{% endcache %}
	
	<a class="back" href="#start" data-controller="back-to-top scroll-to" data-action="click->scroll-to#scroll">
		Back to top
		{{ svg('@webroot/static/svg/arrow-down.svg') }}
	</a>
{% endblock %}

