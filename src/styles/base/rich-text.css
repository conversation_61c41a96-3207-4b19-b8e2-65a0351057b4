.rich-text {
  --gap: 1.5rem;
  --media-gap: 2rem;
  line-height: 1.5;

  @media (--medium-up) {
    --media-gap: 2.5rem;
  }

  p,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  ol,
  ul,
  blockquote {
    max-inline-size: 55rem;
  }

  &--center {    
    p,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    ol,
    ul,
    blockquote,
    img {
      margin-inline: auto;
    }
  }

  > * + * {
    margin-block-start: var(--gap);
  }

  a:not(.button) {
    @mixin link-off;
  }

  ul {
    padding-inline-start: 1em;
    list-style: disc;
  }

  ol {
    padding-inline-start: 1em;
    list-style: decimal;
  }

  li {
    padding-inline-start: 10px;
    margin-block-start: 0.5em;

    &:first-child {
      margin-block-start: 0;
    }
  }

  strong,
  b {
    font-weight: 700;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    &:first-child {
      margin-block-start: 0;
    }
  }
  
  h1,
  h2 {
    @mixin heading-1-sans;
  }
  
  h3 {
    @mixin text-large;
  }

  img,
  video,
  figure,
  svg,
  iframe {
    margin-block-start: var(--media-gap);
    margin-block-end: var(--media-gap);
  }
  
  img {
    aspect-ratio: initial !important;
  }

  figure img {
    margin-block-start: 0;
    margin-block-end: 0;
  }

  video,
  iframe {
    max-inline-size: 100%;
  }

  > {
    img,
    video,
    figure,
    svg,
    iframe {
      &:first-child {
        margin-block-start: 0;
      }

      &:last-child {
        margin-block-end: 0;
      }
    }
  }

  > p:first-child > {
    img,
    video,
    figure,
    svg,
    iframe {
      &:first-child {
        margin-block-start: 0 !important;
      }
    }
  }

  > p:last-child > {
    img,
    video,
    figure,
    svg,
    iframe {
      &:last-child {
        margin-block-end: 0 !important;
      }
    }
  }
}
