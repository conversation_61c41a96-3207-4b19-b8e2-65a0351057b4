:root {
  --section-padding: calc(2vw + 2.825rem);
  --section-inner-padding-x: min(var(--container-padding), 2.375rem);
  --section-inner-padding-y: min(var(--container-padding), 2.5rem);
}

.section {
  --section-padding-top-extra: 0px;
  --section-padding-bottom-extra: 0px;
  position: relative;
  padding-top: calc(var(--section-padding) + var(--section-padding-top-extra));
  padding-bottom: calc(var(--section-padding) + var(--section-padding-bottom-extra));
}

.section.theme--dark-linen + .section.theme--dark-linen {
  --section-padding-top-extra: -50px;
}