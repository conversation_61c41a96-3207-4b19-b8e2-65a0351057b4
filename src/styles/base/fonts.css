/* This file typically only contains @font-face declarations. Font files can be added to/static */

@font-face {
  font-family: 'Agrandir';
  font-weight: 300;
  font-style: normal;
  font-display: block;
  src: url('/static/fonts/agrandir/Agrandir-Narrow.woff') format('woff');
}

@font-face {
  font-family: 'Agrandir';
  font-weight: normal;
  font-style: normal;
  font-display: block;
  src: url('/static/fonts/agrandir/Agrandir-Regular.woff') format('woff');
}

@font-face {
  font-family: 'Agrandir';
  font-weight: 700;
  font-style: normal;
  font-display: block;
  src: url('/static/fonts/agrandir/Agrandir-TextBold.woff') format('woff');
}

@font-face {
  font-family: 'Agrandir';
  font-weight: 900;
  font-style: normal;
  font-display: block;
  src: url('/web/static/fonts/agrandir/Agrandir-GrandHeavy.woff') format('woff');
}

@font-face {
  font-family: 'Recife';
  font-weight: 300;
  font-style: normal;
  font-display: block;
  src: url('/static/fonts/recife/RecifeDisplay-Light.woff') format('woff');
}

@font-face {
  font-family: 'Recife';
  font-weight: normal;
  font-style: normal;
  font-display: block;
  src: url('/static/fonts/recife/RecifeDisplay-Regular.woff') format('woff');
}

@font-face {
  font-family: 'Recife';
  font-weight: 700;
  font-style: normal;
  font-display: block;
  src: url('/static/fonts/recife/RecifeDisplay-Bold.woff') format('woff');
}