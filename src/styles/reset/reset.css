/* Destyle CSS reset: https://github.com/nicolas-cusan/destyle.css. */
@import 'destyle.css';

:where(:focus) {
  @mixin focus-outline;
}

:where(:focus:not(:focus-visible)) {
  outline: none;
}

small {
  font-size: inherit;
}

:where(figcaption) {
  max-inline-size: max-content;
  margin-inline: auto;
}

:where(a:not(.button):focus-visible, button:focus-visible) {
  outline-offset: 0.25em;
  outline-width: 0.25em;
  outline-color: currentColor;
}

:where(ul, ol, dl, dt, dd, p, figure, blockquote) {
  hanging-punctuation: first last;
  text-wrap: pretty;
}