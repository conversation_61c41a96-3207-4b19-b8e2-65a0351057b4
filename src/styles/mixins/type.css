/* Mixins for shared text styles, heading styles, link styles etc would typically go here */

/* Text */
@define-mixin text-large {
  font-family: var(--font-body);
  font-size: utopia.clamp(20, 24);
  line-height: 1.5;
  font-weight: 400;
}

@define-mixin text-base {
  font-family: var(--font-body);
  font-size: 18px;
  line-height: 1.5;
  font-weight: 300;
}

@define-mixin text-small {
  font-family: var(--font-body);
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}


@define-mixin text-tiny {
  font-family: var(--font-body);
  font-size: 12px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
}

/* Titles */
@define-mixin heading-1-sans {
  font-family: var(--font-body);
  font-size: utopia.clamp(24, 32);
  font-style: normal;
  font-weight: 300;
  line-height: normal;
  letter-spacing: 0.1em;
}

@define-mixin heading-1-serif {
  font-family: var(--font-display);
  font-size: utopia.clamp(24, 32);
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.1em;
}

/* Links */
@define-mixin link {
  text-decoration: underline;
  text-decoration-color: transparent;
  text-underline-offset: 0.15em;
  text-decoration-thickness: 1px;
  transition: text-decoration-color var(--transition-duration);

  &:hover {
    text-decoration-color: currentColor;
  }
}

@define-mixin link-off {
  text-decoration: underline;
  text-decoration-color: currentColor;
  text-underline-offset: 0.15em;
  text-decoration-thickness: 1px;
  transition: text-decoration-color var(--transition-duration);

  &:hover {
    text-decoration-color: transparent;
  }
}

