/* Hide element visually but keep available for screen readers
@see https://allyjs.io/tutorials/hiding-elements.html#2017-edition-of-visuallyhidden */
@define-mixin visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  border: 0;
  padding: 0;
  white-space: nowrap;
  clip-path: inset(100%);
  clip: rect(0 0 0 0);
  overflow: hidden;
}

/* Apply the browser-default focus outline to an element */
@define-mixin focus-outline {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}
