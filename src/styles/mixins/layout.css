@define-mixin container-padding {
  padding-right: var(--container-padding);
  padding-left: var(--container-padding);
}

@define-mixin container {
  @mixin container-padding;
  width: 100%;
  max-width: calc(var(--global-width) + (var(--container-padding) * 2));
  margin-right: auto;
  margin-left: auto;
}

@define-mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
