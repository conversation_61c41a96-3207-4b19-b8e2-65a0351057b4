@define-mixin button {
  display: inline-flex;
  padding: 5px 35px 4px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  border: 1px solid var(--gold);
  border-radius: 50px;
  color: var(--slate);
  font-size: 18px;
  font-weight: normal;
  background-color: var(--gold);
  transition: var(--transition-duration);
  
  @media (--small-down) {
    padding-left: 25px;
    padding-right: 25px;
  }
  
  &:hover {
    background-color: var(--slate);
    color: var(--gold);
  }
  
  &[disabled] {
    opacity: 0.5;
  }
}

@define-mixin button-secondary {
  background-color: var(--slate);
  color: var(--white);
  border-color: var(--slate);

  &:hover {
    background-color: var(--white);
    color: var(--slate);
  }
}
