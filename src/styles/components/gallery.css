.gallery {
  position: relative;
  z-index: 1;
  
  .slider {
    overflow: visible;
    margin-inline: auto;
    max-inline-size: 1050px;
  }
  
  .swiper-wrapper {
    align-items: center;
  }
  
  .slider__slide {
    padding-inline-start: 32px;
    padding-inline-end: 32px;
  }
  
  .slider__control--previous {
    transform: translateX(-16px);
  }

  .slider__control--next {
    transform: translateX(16px);
  }
}

.gallery__decor {
  inline-size: 80%;
  margin-inline: auto;
  position: absolute;
  inset-inline-start: 50%;
  inset-block-end: 100%;
  transform: translateX(-50%) translateY(50%);
  z-index: -1;
}

.gallery__header {
  margin-block-end: 50px;

  .rule {
    max-inline-size: 350px;
    margin-inline: auto;
    margin-block-end: 50px;
    
    @media (--large-up) {
      margin-block-end: 85px;
    }
  }

  h1,
  h2 {
    @mixin heading-1-sans;
    padding-block-start: 12px;
    padding-block-end: 5px;
    text-transform: uppercase;
    max-inline-size: 900px;
    text-align: center;
    margin-inline: auto;
  }

  .rich-text {
    @mixin text-large;
    max-inline-size: 948px;
    line-height: 1.5;
    text-align: center;
    margin-inline: auto;
    margin-block-start: 24px;

    strong {
      font-weight: 600;
      color: var(--gold);
    }
  }
}

.gallery__inner {
  position: relative;
  overflow: hidden;
}