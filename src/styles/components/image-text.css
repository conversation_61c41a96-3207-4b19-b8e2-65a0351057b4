.image-text {
  position: relative;
  overflow: hidden;
  z-index: 1;
  
  strong {
    color: var(--gold);
  }
}

.image-text__decor {
  z-index: -1;
  position: absolute;
  inset-block-start: 0;
  inset-inline-start: 50%;
  transform: translateX(-50%);
  inline-size: 90%;
}

.image-text__inner {}

.image-text__header {
  text-align: center;
  max-inline-size: 894px;
  margin-inline: auto;
  inline-size: 100%;
  margin-block-end: 50px;
  
  .rich-text {
    @mixin text-large;
    display: inline-block;
  }
  
  .rule {
    max-inline-size: 400px;
    margin-inline: auto;
    inline-size: 100%;  
    margin-block-start: 50px;
  }
}

.image-text__item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  max-inline-size: 1204px;
  inline-size: 100%;
  margin-inline: auto;

  & + & {
    margin-block-start: 50px;
  }
  
  &:nth-child(odd) {
    flex-direction: row-reverse;
  }
}

.image-text__image {
  inline-size: 100%;
  
  @media (--medium-up) {
    inline-size: 45%;
  }
  
  img {
    max-inline-size: 550px;
    inline-size: 100%;
    margin-inline: auto;
  }
}

.image-text__text {
  inline-size: 100%;
  
  @media (--medium-up) {
    inline-size: 45%;
    max-inline-size: 510px;
  }
}