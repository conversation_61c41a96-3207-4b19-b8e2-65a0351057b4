.rule {
  display: block;
  inline-size: 100%;
  border-block-start: 1px solid var(--gold);
  position: relative;
  margin-block-start: 2px;
  margin-block-end: 2px;

  &::after,
  &::before {
    position: absolute;
    inset-block-start: -3px;
    content: '';
    inline-size: 5px;
    block-size: 5px;
    border-radius: 50%;
    background-color: var(--gold);
  }

  &::before {
    inset-inline-start: 0;
  }

  &::after {
    inset-inline-end: 0;
  }
}