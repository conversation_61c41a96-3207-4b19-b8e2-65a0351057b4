.info {}

.info__inner {}

.info__header {
  position: relative;
  margin-block-end: 50px;
  
  img {
    position: absolute;
    inset-block-start: 50%;
    inset-inline-end: 0;
    transform: translateY(-50%);
    inline-size: 118px;
  }
  
  h2 {
    @mixin heading-1-sans;
    inline-size: 100%;
    text-align: center;
    text-transform: uppercase;
  }
}

.info__grid {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fit, minmax(min(20rem, 100%), 1fr));
}

.info__item {
  position: relative;
  
  h3 {
    text-transform: uppercase;
  }
  
  .rich-text {
    font-size: 16px;
    font-style: italic;
    font-family: var(--font-display);
  }
}

.info__item__text {
  min-height: 113px;
  background-color: var(--linen);
  position: absolute;
  inset-inline-start: 0;
  inset-block-end: 25px;
  padding: 19px 14px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  inline-size: calc(100% - 25px);
}

.info__footer {
  text-align: center;
  inline-size: 100%;
  max-inline-size: 800px;
  margin-inline: auto;
  margin-block-start: 50px;
}