.landing {
  padding-block-end: 100px;
  
  @media (--small-down) {
    padding-block-end: 50px;
  }
}

.landing__inner {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  text-align: center;
  column-gap: 40px;
  
  @media (--small-down) {
    column-gap: 25px;
  }
  
  figure {
    inline-size: 100%;
    max-inline-size: 600px;
    
    img {
      inline-size: auto;
      block-size: auto;
      margin-inline: auto;
      max-block-size: calc(100vh - 316px);
      
      @media (--small-down) {
        max-block-size: calc(100vh - 316px);
      }
    }
  }
  
  h1 {
    @mixin heading-1-sans;
    text-transform: uppercase;
    inline-size: 100%;
    margin-block-start: 25px;
    margin-block-end: 10px;
  }
  
  .rich-text {
    @mixin text-large;
    inline-size: 100%;
    color: var(--grey);
    padding-block-end: 10px;
    
    @media (--small-down) {
      padding-block-end: 0;
    }
    
    p {
      max-inline-size: 100%;
    }
  }
  
  a:not(.button) {
    margin-block-start: 20px;
  }
  
  .button {
    margin-block-start: 20px;
  }
}