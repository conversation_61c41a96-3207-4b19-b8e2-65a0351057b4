.hero {
  position: relative;
  overflow: hidden;
}

.hero__inner {}

.hero__header {
  position: relative;
  text-align: center;

  h2 {
    inline-size: auto;
  }

  .rich-text {
    @mixin text-large;
    max-inline-size: 894px;
    margin-inline: auto;
    padding-block-start: 38px;
    padding-block-end: 58px;

    strong {
      color: var(--gold);
    }
  }
}

.hero__heading {}

.hero__decor {
  position: absolute;
  inset-block-start: 0;
  inset-inline-start: 0;
  inline-size: 14%;

  img {
    max-block-size: 100%;
    inline-size: auto;
  }
}
