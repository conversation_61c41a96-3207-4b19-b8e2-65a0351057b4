.burger {
  --burger-line-width: 1.5rem;
  --burger-line-height: 1.5px;
  --burger-line-gap: 0.25rem;
  position: relative;
  inline-size: var(--burger-line-width);
  margin-inline-start: auto;
  margin-inline-end: auto;
  transition: background-color var(--transition-duration);

  &,
  &::before,
  &::after {
    display: block;
    block-size: var(--burger-line-height);
    background-color: currentColor;
    border-radius: var(--burger-line-height);
    transition: transform var(--transition-duration);
  }

  &::before,
  &::after {
    position: absolute;
    inset-inline-start: 0;
    content: '';
  }

  &::before {
    inset-block-start: calc((var(--burger-line-gap) + var(--burger-line-gap)) * -1);
    inline-size: var(--burger-line-width);
  }

  &::after {
    inline-size: var(--burger-line-width);
    inset-block-end: calc((var(--burger-line-gap) + var(--burger-line-gap)) * -1);
  }

  .header.is-open & {
    background-color: transparent;

    &::before {
      transform: translateY(calc(var(--burger-line-gap) + var(--burger-line-gap))) rotate(45deg);
    }

    &::after {
      transform: translateY(calc((var(--burger-line-gap) + var(--burger-line-gap)) * -1))
        rotate(-45deg);
      inline-size: var(--burger-line-width);
    }
  }
}
