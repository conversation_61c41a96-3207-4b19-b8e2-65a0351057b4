.layered-scroll {
  position: relative;
  background-color: var(--linen);
}

.layered-scroll.is-enabled {
  /* Additional padding will be added via JS based on number of sections */
}

.layered-scroll__section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background-color: var(--linen);
  border-radius: 0;
  overflow: hidden;
  z-index: 1;
  
  &:nth-child(even) {
    background-color: var(--linen-dark);
  }
  
  /* When stacking is enabled, sections become sticky */
  .layered-scroll.is-enabled & {
    position: sticky;
    top: 0;
    border-radius: 0 0 24px 24px;
    
    @media (--medium-up) {
      border-radius: 0 0 48px 48px;
    }
  }
}

.layered-scroll__content {
  @mixin container;
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
  align-items: center;
  width: 100%;
  padding-block: 80px;
  
  @media (--medium-up) {
    grid-template-columns: 1fr 1fr;
    gap: 64px;
    padding-block: 120px;
  }
  
  @media (--large-up) {
    gap: 80px;
  }
}

.layered-scroll__image {
  position: relative;
  aspect-ratio: 4/3;
  border-radius: 12px;
  overflow: hidden;
  
  @media (--medium-up) {
    aspect-ratio: 3/4;
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
}

.layered-scroll__text {
  display: flex;
  flex-direction: column;
  gap: 24px;
  
  @media (--medium-up) {
    gap: 32px;
  }
}

.layered-scroll__title {
  @mixin heading-1-serif;
  color: var(--slate);
  margin: 0;
}

.layered-scroll__description {
  @mixin text-large;
  color: var(--grey);
  margin: 0;
  line-height: 1.6;
}

/* Alternating layout for visual interest */
.layered-scroll__section:nth-child(even) .layered-scroll__content {
  @media (--medium-up) {
    grid-template-columns: 1fr 1fr;
    
    .layered-scroll__image {
      order: 2;
    }
    
    .layered-scroll__text {
      order: 1;
    }
  }
}

/* Animation states */
.layered-scroll__section {
  .layered-scroll__image,
  .layered-scroll__text {
    transition: 
      opacity 0.8s var(--easeOutQuart),
      transform 0.8s var(--easeOutQuart);
  }
  
  &:not(.is-visible) {
    .layered-scroll__image {
      opacity: 0;
      transform: translateY(40px);
    }
    
    .layered-scroll__text {
      opacity: 0;
      transform: translateY(20px);
    }
  }
}

/* Overlay effect for stacked sections */
.layered-scroll__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 2;
}

/* Responsive adjustments */
@media (--small-down) {
  .layered-scroll__content {
    padding-block: 60px;
    gap: 24px;
  }
  
  .layered-scroll__text {
    gap: 20px;
  }
  
  .layered-scroll__title {
    font-size: utopia.clamp(20, 24);
  }
}
