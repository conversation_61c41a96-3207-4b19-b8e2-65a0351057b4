:root {
  --accordion-transition-duration: 0.5s;
  --accordion-transition-ease: ease-in-out-cubic;
}

.enquiries {}

.enquiries__header {
  position: relative;
  text-align: center;
  z-index: 0;
  
  img {
    position: absolute;
    inset-block-start: 50%;
    transform: translateY(-60%);
    inline-size: 40%;
    z-index: -1;
  }
  
  img:nth-child(1) {
    inset-inline-start: 0;
  }

  img:nth-child(2) {
    inset-inline-end: 0;
  }

  .rich-text {
    margin-block-start: 40px;
  }
  
  strong {
    color: var(--gold);
  }
  
  a {
    font-weight: 700;
  }
}

.enquiries__heading {}

.enquiries__orders {
  background-color: var(--linen-dark);
  inline-size: 100%;
  max-inline-size: 739px;
  margin-inline: auto;
  padding: 25px;
  margin-block-start: 42px;
  
  @media (--large-up) {
    padding-inline-start: 39px;
    padding-inline-end: 39px;
  }
}

.enquiries__orders__header {
  text-align: center;
  margin-block-end: 22px;
  
  p {
    @mixin text-large;
  }
}

.enquiries__orders__controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-block-end: 37px;
}

.enquiries__orders__controls__inner {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background-color: var(--white);
  border: 1px solid;
  padding: 5px;
  border-radius: 50px;
}

.enquiries__orders__control {
  @media (--medium-up) {
    min-inline-size: 196px;
  }
  
  &:not(.is-selected) {
    background-color: var(--white);
    color: var(--black);
    border-color: var(--white);
  }
}

.enquiries__orders__contents {
  position: relative;
  overflow: hidden;
}

.enquiries__orders__content {
  transition: var(--transition-duration);
  inset: 0;
  
  &:not(.is-selected) {
    position: absolute;
    opacity: 0;
    visibility: hidden;
  }
  
  .rich-text {
    margin-block-end: 24px;
  }
}

.enquiries__orders__item {
  display: flex;
  gap: 24px;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  
  & + & {
    margin-block-start: 24px;
  }
}

.enquiries__orders__image {
  inline-size: 100%;
  
  @media (--large-up) {
    inline-size: calc(50% - 12px);
  }
}

.enquiries__orders__text {
  inline-size: 100%;
  
  @media (--large-up) {
    inline-size: calc(50% - 12px);
  }
  
  a {
    @mixin link-off;
    font-weight: 700;
  }
}

.enquiries__orders__form {
  [type="radio"],
  [type="checkbox"] {
    appearance: auto;
  }

  select.freeform-input {
    background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23131313%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
    background-repeat: no-repeat;
    background-position: right 0.7rem top 50%;
    background-size: 0.65rem auto;
  }

  .freeform-label {
    font-weight: normal !important;
    margin-block-end: 5px;
  }

  .freeform-input:not([type="checkbox"]):not([type="radio"]) {
    padding: 13px 14px;
    min-block-size: 55px;
    background-color: var(--white);
    border: 1px solid;
    
    &::placeholder {
      color: #BFBFBE;
    }
  }

  .freeform-column {
    margin-block-end: 15px;
    
    @media (--small-down) {
      inline-size: 100%;
    }
  }
  
  textarea.freeform-input {
    min-block-size: 265px !important;
  }

  [type="submit"] {
    @mixin button;
    @mixin button-secondary;
    inline-size: 100%;
    min-block-size: 40px;
  }
  
  .freeform-processing {
    &::before {
      content: normal !important;
    }
  }

  .freeform-button-container {
    > * {
      inline-size: 50%;
    }
  }

  .freeform-button-wrapper {
    inline-size: 100%;
  }

  .freeform-errors {
    margin-block-start: 5px;
  }

  .freeform-form-errors {
    text-align: center;
    border-radius: 0;
    padding: 10px;
    color: var(--black);
    border: 0;
    font-weight: 700;
    margin-block-end: 10px;
  }

  .freeform-form-success {
    padding: 10px;
    text-align: center;
    font-weight: 700;
    margin-block-end: 10px;
    background-color: var(--gold-light);
  }
}

.enquiries__faq {
  inline-size: 100%;
  max-inline-size: 739px;
  margin-inline: auto;
  counter-reset: section;
  margin-block-start: 62px;
}

.enquiries__faq__header {
  padding-block-end: 15px;
  border-block-end: 1px solid;
  
  h2 {
    @mixin text-large;
    text-transform: uppercase;
  }
}

.enquiries__faq__item {
  counter-increment: section;
  border-block-end: 1px solid #DDD9D8;
}

.enquiries__faq__item__trigger {
  @mixin text-large;
  font-family: var(--font-display);
  padding-block-start: 18px;
  padding-block-end: 18px;
  inline-size: 100%;
  display: flex;
  align-items: center;
  
  &::before {
    inline-size: 50px;
    min-inline-size: 50px;
    content: counter(section, decimal-leading-zero);
  }
  
  span {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-inline-start: auto;
    border-radius: 300px;
    inline-size: 32px;
    min-inline-size: 32px;
    block-size: 32px;
    border: 1px solid rgba(255, 255, 255, 0.14);
    background: var(--white);
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(9.300000190734863px);
    transition: var(--transition-duration);
  }
  
  &.is-active span {
    background-color: var(--black);
    color: var(--white);
  }
  
  svg {
    transition: var(--transition-duration);
  }
  
  &.is-active svg {
    transform: scaleY(-1);
  }
}

.enquiries__faq__item__content {
  position: relative;
  z-index: 1;
  block-size: 0;
  overflow: hidden;
  transition-property: block-size, opacity, visibility;
  transition-timing-function: var(--accordion-transition-ease);
  transition-duration: var(--accordion-transition-duration);

  .accordion__item__trigger:not(.is-active) + & {
    opacity: 0;
    visibility: hidden;
  }
}

.enquiries__faq__item__content__inner {
  padding-block-end: 30px;
}