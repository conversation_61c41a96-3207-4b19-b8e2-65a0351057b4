.petals {}

.petals__inner {
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.petals__header {
  position: relative;
  inline-size: auto;
  padding-block-end: 25px;
  
  &:has(img) {
    inline-size: 100%;
  }

  svg {
    inline-size: 32px;
    min-inline-size: 32px;
    margin-inline: auto;
    display: block;
    color: var(--gold);
    margin-block-end: 63px;

    @media (--small-down) {
      margin-block-end: 50px;
    }
  }
  
  img {
    position: absolute;
    inset-block-start: 50%;
    inset-inline-end: 0;
    transform: translateY(-50%);
    inline-size: 118px;
  }
  
  .petals--decor & img {
    transform: translateY(-100%);
    
    @media (--small-down) {
      transform: translateY(-170%);
    }
  }

  h1,
  h2 {
    @mixin heading-1-sans;
    padding-block-start: 12px;
    padding-block-end: 5px;
    text-transform: uppercase;
    max-inline-size: 900px;
    text-align: center;
    margin-inline: auto;
  }

  .petals--decor & .rule {
    display: none;
  }

  .rich-text {
    @mixin text-large;
    max-inline-size: 948px;
    line-height: 1.5;
    text-align: center;
    margin-inline: auto;
    margin-block-start: 24px;

    strong {
      font-weight: 600;
      color: var(--gold);
    }
  }
}

.petals__grid {
  inline-size: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  align-items: flex-start;
  justify-content: space-between;
  max-inline-size: 1038px;
  margin-inline: auto;
}

.petals__item {
  position: relative;
  inline-size: calc(50% - 12px);
  text-align: center;
  color: var(--gold);

  @media (--large-up) {
    inline-size: calc(33.33% - 18px);
  }

  figure {
    margin-block-end: 25px;
  }

  a {
    @mixin link;

    &::after {
      position: absolute;
      inset: 0;
      content: '';
      z-index: 1;
    }
  }
}

.petals__links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  margin-block-start: 50px;

  @media (--large-up) {
    gap: 40px;
  }
}

.petals__decor {
  margin-block-start: 50px;
  display: flex;
  align-items: center;
  inline-size: 100%;
  max-inline-size: 1038px;
  margin-inline: auto;
  column-gap: 83px;
  
  @media (--small-down) {
    column-gap: 25px;
  }

  svg {
    min-inline-size: 32px;
    inline-size: 32px;
    color: var(--gold);
  }

  .rule {
    inline-size: 100%;
  }
}