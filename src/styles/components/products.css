.products {}

.products__list {
  &.is-enabled {
    display: grid;
  }
}

.products__item {
  background-color: var(--linen);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 24px;
  border-block-start: 2px solid var(--linen-dark);
  padding: 25px;

  @media (--large-up) {
    position: sticky;
    inset-block-start: 0;
    inset-inline-start: 0;
    transform-origin: 50% 0;
    padding: 70px 72px 45px 107px;
  }
  
  & + & {
    margin-block-start: 32px;
  }
}

.products__gallery {
  inline-size: 100%;
  text-align: center;
  
  @media (--medium-up) {
    inline-size: calc(50% - 12px);
    max-inline-size: 500px;
  }
}

.products__content {
  display: flex;
  flex-direction: column;
  inline-size: 100%;
  
  @media (--medium-up) {
    inline-size: calc(50% - 12px);
    max-inline-size: 555px;
  }
  
  h2 {
    @mixin heading-1-serif;
    text-transform: uppercase;
  }
}

.products__content__inner {
  inline-size: 100%;
}

.products__sub {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  margin-block-end: 26px;
  margin-block-start: 28px;
  
  > p {
    font-weight: 600;
  }
}

.products__tag {
  position: relative;
  
  > p {
    @mixin text-small;
    padding: 5px 12px 2px;
    background-color: var(--gold-light);
    cursor: default;
  }
}

.products__tag__hover {
  position: absolute;
  inset-block-end: 120%;
  inset-inline-start: 50%;
  transform: translateX(-50%);
  padding: 15px 20px 11px;
  background-color: var(--white);
  border: 1px solid var(--gold);
  font-size: 16px;
  inline-size: 194px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.18);
  text-align: center;
  transition: var(--transition-duration) var(--transition-duration);
  
  .products__tag:not(:hover) & {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(20px);
  }
}

.products__desc {
  color: var(--grey);
  max-inline-size: 495px;
  font-size: 16px;
}

.products__available {
  padding: 15px;
  background-color: var(--linen-dark);
  display: flex;
  gap: 12px;
  align-items: center;
  margin-block-start: 32px;
  margin-block-end: 40px;
  max-inline-size: 495px;
  
  @media (--large-up) {
    gap: 25px 50px;
    padding: 15px 25px;
  }
  
  @media (--medium-down) {
    flex-wrap: wrap;
  }
}

.products__available__title {
  @mixin text-tiny;
  text-transform: uppercase;
  font-weight: 600;
}

.products__available__list {
  display: flex;
  gap: 24px;
  align-items: center;
  
  @media (--large-up) {
    gap: 25px 50px;
  }

  @media (--small-down) {
    flex-wrap: wrap;
  }
}

.products__available__item {
  position: relative;
  
  button {
    @mixin link-off;
    @mixin text-tiny;
    font-weight: 600;
  }
}

.products__available__hover {
  position: absolute;
  inset-block-end: 120%;
  inset-inline-start: 50%;
  transform: translateX(-50%);
  padding: 15px 20px 11px;
  background-color: var(--white);
  border: 1px solid var(--gold);
  font-size: 16px;
  inline-size: 194px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.18);
  text-align: center;
  transition: var(--transition-duration) var(--transition-duration);
  
  .products__available__item:not(:hover) & {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(20px);
  }
}

.products__icons {
  margin-block-start: auto;
  inline-size: 100%;
  padding-block-start: 25px;
  
  ul {
    display: flex;
    align-items: flex-start;
    justify-content: space-evenly;
    flex-wrap: wrap;
    gap: 15px;
  }
  
  li {
    inline-size: 88px;
  }
  
  svg {
    inline-size: 32px;
    block-size: 32px;
    margin-inline-start: auto;
    margin-inline-end: auto;
  }
  
  p {
    @mixin text-tiny;
    max-inline-size: 88px;
    font-style: italic;
    margin-block-start: 10px;
    margin-inline-start: auto;
    margin-inline-end: auto;
    text-align: center;
    font-family: var(--font-display);
  }
}

.products__overlay {
  position: absolute;
  inset: 0;
  pointer-events: none;
  background-color: var(--black);
  opacity: 0;
  z-index: 1;
}
