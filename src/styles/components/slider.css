.slider {
  --slider-gap: 0px;
  position: relative;
  overflow: hidden;
  inline-size: 100%;
}

.slider__track {
  display: flex;
  gap: var(--slider-gap);
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  overflow-x: auto;
  scrollbar-width: none;
  -webkit-overflow-scrolling: touch;
  cursor: grab;
  user-select: none;

  /* Prevents slider swiping triggering page back/forwards in some browsers */
  overscroll-behavior-x: contain;

  &.is-dragging {
    scroll-snap-type: none;

    > * {
      pointer-events: none;
    }
  }

  &.is-mouse-down {
    cursor: grabbing;
  }

  &::-webkit-scrollbar {
    display: none;
  }

  > * {
    inline-size: 100%;
    flex-shrink: 0;
    scroll-snap-align: start;
  }
}

.slider__slide {
  inline-size: 100%;
  min-inline-size: 100%;
}

.slider__controls {
  display: inline-flex;
  justify-content: center;
  border-radius: 300px;
  border: 1px solid rgba(255, 255, 255, 0.14);
  background: rgba(255, 255, 255, 0.46);
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(9.300000190734863px);
  min-block-size: 39px;
  padding-inline-start: 20px;
  padding-inline-end: 20px;
  margin-block-start: 25px;
}

.slider__controls-fixed {
  position: absolute;
  inset: 0;
  justify-content: space-between;
  align-items: center;
  display: flex;
  z-index: 1;
  pointer-events: none;
}

.slider__control {
  pointer-events: auto;
  transition: var(--transition-duration);
  inline-size: 14px;
  
  .slider__controls-fixed & {
    inline-size: 32px;
    min-inline-size: 32px;
    block-size: 32px;
    min-block-size: 32px;
    border-radius: 300px;
    border: 1px solid rgba(255, 255, 255, 0.14);
    background: var(--white);
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(9.300000190734863px);  
    display: flex;
    justify-content: center;
    align-items: center;
    transition: var(--transition-duration);
    
    &:hover {
      background-color: var(--black);
      color: var(--white);
    }
    
    &[disabled] {
      opacity: 0.5;
    }
  }

  &[disabled] {
    color: #BFBFBE;
  }
}

.slider__dots {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;

  .swiper-pagination-bullet {
    background-color: #DDD9D8;
    inline-size: 9px;
    block-size: 9px;
    border-radius: 50%;
    transition: var(--transition-duration);
    
    &.swiper-pagination-bullet-active {
      background-color: var(--grey);
    }
  }
}