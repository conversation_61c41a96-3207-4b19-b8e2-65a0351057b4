.header {
  position: relative;
  z-index: 1;
  padding-block-start: 10px;
  padding-block-end: 10px;
}

.header__inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 25px;
}

.header__left {
  img {
    inline-size: 90px;
    
    @media (--small-down) {
      inline-size: 65px;
    }
  }
}

.header__right {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 25px;
  
  @media (--small-down) {
    flex-direction: row-reverse;
  }

  a:not(.button) {
    @mixin link;
    text-underline-offset: 0.35em;

    &.is-current {
      text-decoration: underline;
    }
  }
  
  .is-home & a:not(.button) {
    display: none;
  }
}

.header__list {
  position: relative;
  
  button {
    @media (--medium-up) {
      display: none;
    }
    
    .is-home & {
      display: none;
    }  
  }
  
  ul {
    @media (--medium-up) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 25px;    
    }
    
    @media (--small-down) {
      inline-size: 150px;
      position: absolute;
      inset-block-start: calc(100% + 15px);
      inset-inline-end: -15px;
      background-color: var(--white);
      border: 1px solid;
      padding: 20px;
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      transition: var(--transition-duration);
      
      &::after {
        position: absolute;
        inset-block-start: 0;
        inset-inline-end: 0;
        transform: translateX(-70%) translateY(-55%) rotate(-45deg);
        content: '';
        inline-size: 20px;
        background-color: var(--white);
        z-index: 1;
        block-size: 20px;
        border-block-start: 1px solid;
        border-inline-end: 1px solid;
      }
      
      li {
        inline-size: 100%;
      }
      
      .header:not(.is-open) & {
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
      }
    }
  }
}