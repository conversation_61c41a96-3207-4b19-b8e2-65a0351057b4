.back {
  position: fixed;
  inset-block-end: 20px;
  inset-inline-end: 20px;
  z-index: 10;
  border-radius: 300px;
  border: 1px solid rgba(255, 255, 255, 0.14);
  background:var(--white);
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(9.300000190734863px);
  padding: 5px 20px 3px;
  font-size: 14px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  transition: var(--transition-duration);

  &:not(.is-active) {
    opacity: 0;
    visibility: hidden;
  }

  svg {
    transform: rotate(180deg);
  }
}