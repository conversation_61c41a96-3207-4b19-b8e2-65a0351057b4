.process {}

.process__header {
  text-align: center;
  margin-block-end: 50px;

  h2 {
    @mixin heading-1-sans;
    text-transform: uppercase;
    inline-size: 100%;
    margin-block-end: 10px;
  }

  .rich-text {
    @mixin text-large;
    inline-size: 100%;
    color: var(--grey);
    padding-block-end: 15px;

    p {
      max-inline-size: 100%;
    }
  }
}

.process__list {
  &.is-enabled {
    display: grid;
  }
}

.process__item {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: space-between;
  padding-block-end: 54px;
  background-color: var(--linen-dark);
  gap: 24px;

  position: sticky;
  inset-block-start: 0;
  inset-inline-start: 0;
  transform-origin: 50% 0;

  @media (--small-down) {
    padding: 24px;
  }
  
  @media (--small-down) {
    &:not(:last-child) {
      padding-block-end: 35px;
    }
  }

  .rule {
    position: relative;
    inset-block-start: -3px;
    inline-size: 100%;
    margin-block-end: 10px;
  }
}

.process__gallery {
  inline-size: 100%;

  @media (--medium-up) {
    inline-size: calc(50% - 12px);
    max-inline-size: 590px;
  }
}

.process__content {
  inline-size: 100%;

  @media (--medium-up) {
    inline-size: calc(50% - 12px);
    max-inline-size: 700px;
  }

  h3 {
    @mixin heading-1-serif;
    margin-block-end: 40px;
  }

  .rich-text {
    inline-size: 100%;
    line-height: 1.55;
    max-inline-size: 585px;
  }
}

.process__desc {}

.process__footer {
  font-size: 14px;
  font-weight: 600;
  margin-block-start: 23px;
}