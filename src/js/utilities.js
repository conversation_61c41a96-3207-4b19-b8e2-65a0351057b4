let uniqueIdIndex = 0

export const uniqueId = (prefix = '_') => {
  uniqueIdIndex += 1
  return `${prefix}${uniqueIdIndex}`
}

export const elementId = () => uniqueId('_element_')

export const clamp = (target, min, max) => Math.min(Math.max(target, min), max)

export const isTouch = () => window.matchMedia('(hover: none)').matches

let headerTarget
export const getHeaderTarget = () => {
  if (!headerTarget) {
    headerTarget = document.getElementById('header')
  }

  return headerTarget
}

export const mapValueToRange = (value, fromMin, fromMax, toMin, toMax) =>
  toMin + ((value - fromMin) / (fromMax - fromMin)) * (toMax - toMin)

export const isReducedMotion = () => window.matchMedia('(prefers-reduced-motion: reduce)').matches

export const scrollToPosition = ({ target, top, left, behavior }) => {
  target ||= window
  top ||= 0
  left ||= 0
  behavior ||= 'smooth'

  if (behavior === 'smooth' && isReducedMotion()) {
    behavior = 'auto'
  }

  target.scrollTo({
    top,
    left,
    behavior,
  })
}

export const disableBodyScroll = () => {
  document.body.style.overflow = 'hidden'
  document.body.style.paddingRight = 'var(--scrollbar-width)'
}

export const enableBodyScroll = () => {
  document.body.style.overflow = ''
  document.body.style.paddingRight = ''
}

export const parseJson = (json) => {
  if (!json) return {}

  let parsed

  try {
    parsed = JSON.parse(json)
  } catch (error) {
    parsed = {}
  }

  return parsed
}
