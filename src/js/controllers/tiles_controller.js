import { clamp, mapValueToRange } from '../utilities'
import Controller from './controller'

export default class extends Controller {
  static get targets() {
    return ['overlay', 'tile']
  }

  initialize() {
    if (this.tileTargets.length < 2) return    

    this.resize = this.resize.bind(this)
    this.scroll = this.scroll.bind(this)
    this.tiles = new Array(this.tileTargets.length)
    this.offset = new Number(this.element.getAttribute('data-gap'))

    this.resize()
    window.addEventListener('resize', this.resize)

    if (this.isEnabled) {
      this.scroll()
    }
  }

  enable() {
    if (this.isEnabled) return
    this.isEnabled = true

    this.element.classList.add('is-enabled')
    this.element.style.paddingBottom = `${this.gap * (this.tileTargets.length - 1)}px`
    window.addEventListener('scroll', this.scroll, { passive: true })
  }

  disable() {
    if (!this.isEnabled) return
    this.isEnabled = false

    this.element.classList.remove('is-enabled')
    this.element.style.paddingBottom = ''
    this.tileTargets.forEach((tile) => {
      tile.style.top = ''
      tile.style.transform = ''
    })
    this.overlayTargets.forEach((overlay) => {
      overlay.style.opacity = ''
    })

    window.removeEventListener('scroll', this.scroll)
  }

  resize() {
    this.gap = clamp(mapValueToRange(window.innerWidth, 385, 1440, 8, this.offset), 8, this.offset)
    const tileHeight = Math.max(
      ...this.tileTargets.map((tile) => tile.getBoundingClientRect().height),
    )

    if (tileHeight > window.innerHeight) {
      this.disable()
      return
    } else {
      this.enable()
    }

    this.scrollY = window.scrollY
    const wrapTop = this.element.getBoundingClientRect().top
    
    if (tileHeight)
      this.tileTargets.forEach((tile, index) => {
        const topOffset = 0
        tile.style.top = `${topOffset}px`
        this.tiles[index] = this.tiles[index] || {}
        this.tiles[index].progress = -1
        this.tiles[index].start = wrapTop + tileHeight * index + this.scrollY - topOffset
      })

    this.tileTargets.forEach((_, index) => {
      this.tiles[index].end = this.tiles[this.tileTargets.length - 1].start
    })

    this.update()
  }

  scroll() {
    this.scrollY = window.scrollY
    this.update()
  }

  update() {
    this.tiles.forEach((tile, index) => {
      if (index === this.tiles.length - 1) {
        this.tileTargets[index].style.transform = `translateY(${this.gap * index}px)`
      } else {
        const progress = clamp(mapValueToRange(this.scrollY, tile.start, tile.end, 0, 1), 0, 1)

        if (progress !== tile.progress) {
          tile.progress = progress
          this.tileTargets[index].style.transform = `translateY(${
            this.gap * index
          }px)`
          
          if (this.overlayTargets[index]) {
            this.overlayTargets[index].style.opacity = clamp(
              mapValueToRange(
                this.scrollY,
                tile.start,
                this.tiles[this.tiles.length - 1].end,
                0,
                0.5,
              ),
              0,
              0.5,
            )
          }
        }
      }
    })
  }
}
