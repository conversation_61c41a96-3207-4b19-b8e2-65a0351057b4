import Swiper from 'swiper';
import {
  A11y,
  Mousewheel,
  Navigation,
  Pagination,
  Autoplay,
  Thumbs,
  EffectFade,
} from 'swiper/modules'

import Controller from './controller'

export default class extends Controller {
  static get targets() {
    return ['slides', 'previous', 'next', 'pagination', 'count']
  }

  static get options() {
    return  {
      modules: [A11y, Mousewheel, Navigation, Pagination, Autoplay, Thumbs, EffectFade],
      grabCursor: true,
      loop: false,
      mousewheel: {
        forceToAxis: true,
      },
      preloadImages: false,
      updateOnImagesReady: false,
      slidesPerView: 'auto',
      centeredSlides: false,
      loopFillGroupWithBlank: true,
    }
  }

  initialize() {
    if (this.options.thumbContainer) {
      this.thumbJazz()
    } else {
      this.createSwiper()
    }
    this.swiper.on('afterInit', () => {
      this.emit('slider:show', {}, this.swiper.slides[this.swiper.activeIndex])
    })
    this.swiper.on('activeIndexChange', () => {
      this.emit('slider:show', {}, this.swiper.slides[this.swiper.activeIndex])
      
      if (this.hasCountTarget) {
        var c = this.swiper.activeIndex + 1
        this.countTarget.textContent = (c < 10) ? '0' + c : c
      }  
    })
  }

  thumbJazz() {
    const slider = document.querySelector(this.options.thumbContainer)

    this.thumbInterval = setInterval(() => {
      this.sliderController = this.application.getControllerForElementAndIdentifier(
        slider,
        'slider',
      )

      if (this.sliderController) {
        clearInterval(this.thumbInterval)

        this.options.thumbs = {
          swiper: this.sliderController.swiper,
        }

        this.createSwiper()
      }
    }, 50)
  }

  createSwiper() {
    if (this.hasNextTarget) {
      this.options.navigation ||= {}
      this.options.navigation.nextEl = this.nextTarget
    }

    if (this.hasPreviousTarget) {
      this.options.navigation ||= {}
      this.options.navigation.prevEl = this.previousTarget
    }

    if (this.hasPaginationTarget) {
      this.options.pagination ||= {
        clickable: true,
      }
      this.options.pagination.el = this.paginationTarget
    }

    const slidesTarget = this.hasSlidesTarget ? this.slidesTarget : this.element
    this.swiper = new Swiper(slidesTarget, this.options)
  }
}
