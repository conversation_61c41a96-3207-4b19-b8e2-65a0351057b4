import Controller from './controller'

export default class extends Controller {
  static targets = ['content', 'trigger']

  static options = {
    multipleOpen: false,
  }

  initialize() {
    this.element.addEventListener('transitionend', (ev) => {
      if (ev.target.dataset.accordionTarget === 'content' && ev.propertyName === 'height') {
        if (ev.target.getBoundingClientRect().height > 0) {
          ev.target.style.height = 'auto'

          // Remove `overflow: hidden` when accordion is open, so that `position: sticky` works within accordion content
          ev.target.style.overflow = 'visible'
        } else {
          ev.target.style.height = ''
        }

        window.dispatchEvent(new Event('resize'))
      }
    })

    // If URL hash matches the ID of an accordion trigger, open that item
    if (location.hash) {
      const id = location.hash.replace('#', '')
      const matchingTrigger = this.triggerTargets.find((trigger) => trigger.id === id)

      if (matchingTrigger) {
        this.open(matchingTrigger)
      }
    }
  }

  toggle(event) {
    if (event.currentTarget.classList.contains('is-active')) {
      this.close(event.currentTarget)
    } else {
      if (!this.options.multipleOpen) {
        for (let index = 0; index < this.triggerTargets.length; index++) {
          if (this.triggerTargets[index].classList.contains('is-active')) {
            this.close(this.triggerTargets[index])
          }
        }
      }

      this.open(event.currentTarget)
    }
  }

  open(trigger) {
    trigger.classList.add('is-active')

    const content = trigger.nextElementSibling
    const contentInner = content.firstElementChild

    const height = contentInner.getBoundingClientRect().height
    this.emitHeight(height)
    content.style.height = `${height}px`

    this.emit('accordion:show', {}, trigger)
  }

  close(trigger) {
    trigger.classList.remove('is-active')

    const content = trigger.nextElementSibling

    const height = content.getBoundingClientRect().height
    this.emitHeight(-height)
    content.style.height = `${height}px`
    content.style.overflow = ''

    requestAnimationFrame(() => {
      content.style.height = '0'
    })

    this.emit('accordion:hide', {}, trigger)
  }

  emitHeight(heightChange) {
    // Trigger event with the changing accordion height as data
    // This is used by graphic-accordion_controller to sync the graphic to the accordion's height,
    // before having to wait for the height transition to complete
    this.emit('accordion:height', {
      bubbles: true,
      detail: heightChange,
    })
  }
}
