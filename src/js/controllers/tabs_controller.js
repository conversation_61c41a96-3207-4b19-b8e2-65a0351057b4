import Controller from './controller'
export default class extends Controller {
  static get targets() {
    return ['content', 'link']
  }

  static get values() {
    return {
      smoothScroll: Boolean,
    }
  }

  initialize() {
    this.selectedClass = 'is-selected'

    const selectedLink = this.linkTargets.find((link) =>
      link.classList.contains(this.selectedClass),
    )
    if (selectedLink) {
      this.selectedIndex = this.linkTargets.indexOf(selectedLink)
    }
  }

  select(ev) {
    ev.preventDefault()

    const index = this.linkTargets.indexOf(ev.currentTarget)
    if (index === this.selectedIndex) return

    this.emit('tabs:beforechange', { bubbles: true })

    this.linkTargets[index].classList.add(this.selectedClass)
    this.contentTargets[index].classList.add(this.selectedClass)

    if (this.selectedIndex != null && this.selectedIndex > -1) {
      this.linkTargets[this.selectedIndex].classList.remove(this.selectedClass)
      this.contentTargets[this.selectedIndex].classList.remove(this.selectedClass)
    }

    this.selectedIndex = index
  }
}
