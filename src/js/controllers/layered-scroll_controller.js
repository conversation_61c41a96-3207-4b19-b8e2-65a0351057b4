import { clamp, mapValueToRange } from '../utilities'
import Controller from './controller'

export default class extends Controller {
  static get targets() {
    return ['section', 'overlay']
  }

  static get options() {
    return {
      // Gap between stacked sections in pixels
      gap: 24,
      // Minimum section height relative to viewport
      minSectionHeight: 0.8,
      // Threshold for enabling the effect (sections must be taller than viewport * threshold)
      enableThreshold: 0.6
    }
  }

  initialize() {
    this.resize = this.resize.bind(this)
    this.scroll = this.scroll.bind(this)
    this.sections = new Array(this.sectionTargets.length)
    this.isEnabled = false
    
    this.resize()
    window.addEventListener('resize', this.resize)
    
    // Initialize intersection observer for visibility animations
    this.initializeVisibilityObserver()
  }

  disconnect() {
    window.removeEventListener('resize', this.resize)
    window.removeEventListener('scroll', this.scroll)
    
    if (this.visibilityObserver) {
      this.visibilityObserver.disconnect()
    }
  }

  initializeVisibilityObserver() {
    this.visibilityObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('is-visible')
          }
        })
      },
      {
        threshold: 0.2,
        rootMargin: '-10% 0px'
      }
    )

    this.sectionTargets.forEach((section) => {
      this.visibilityObserver.observe(section)
    })
  }

  enable() {
    if (this.isEnabled) return
    this.isEnabled = true

    this.element.classList.add('is-enabled')
    
    // Add padding to container to allow for stacking effect
    const totalGap = this.options.gap * (this.sectionTargets.length - 1)
    this.element.style.paddingBottom = `${totalGap}px`
    
    window.addEventListener('scroll', this.scroll, { passive: true })
    this.scroll()
  }

  disable() {
    if (!this.isEnabled) return
    this.isEnabled = false

    this.element.classList.remove('is-enabled')
    this.element.style.paddingBottom = ''
    
    // Reset all section transforms and overlays
    this.sectionTargets.forEach((section) => {
      section.style.transform = ''
      section.style.zIndex = ''
    })
    
    this.overlayTargets.forEach((overlay) => {
      overlay.style.opacity = ''
    })

    window.removeEventListener('scroll', this.scroll)
  }

  resize() {
    const sectionHeight = Math.max(
      ...this.sectionTargets.map((section) => section.getBoundingClientRect().height)
    )
    
    // Enable effect only if sections are tall enough
    if (sectionHeight < window.innerHeight * this.options.enableThreshold) {
      this.disable()
      return
    } else {
      this.enable()
    }

    this.scrollY = window.scrollY
    const containerTop = this.element.getBoundingClientRect().top + this.scrollY
    
    // Calculate scroll positions for each section
    this.sectionTargets.forEach((section, index) => {
      this.sections[index] = this.sections[index] || {}
      this.sections[index].element = section
      this.sections[index].start = containerTop + (sectionHeight * index)
      this.sections[index].end = containerTop + (sectionHeight * (index + 1))
      this.sections[index].progress = -1
    })

    this.update()
  }

  scroll() {
    this.scrollY = window.scrollY
    this.update()
  }

  update() {
    if (!this.isEnabled) return

    this.sections.forEach((section, index) => {
      const isLastSection = index === this.sections.length - 1
      
      if (isLastSection) {
        // Last section doesn't move
        section.element.style.transform = `translateY(${this.options.gap * index}px)`
        section.element.style.zIndex = this.sections.length - index
      } else {
        // Calculate progress through this section's scroll range
        const progress = clamp(
          mapValueToRange(this.scrollY, section.start, section.end, 0, 1),
          0,
          1
        )

        if (progress !== section.progress) {
          section.progress = progress
          
          // Apply transform to create stacking effect
          const translateY = this.options.gap * index
          section.element.style.transform = `translateY(${translateY}px)`
          section.element.style.zIndex = this.sections.length - index
          
          // Apply overlay opacity based on scroll progress
          if (this.overlayTargets[index]) {
            const overlayOpacity = clamp(
              mapValueToRange(progress, 0.5, 1, 0, 0.3),
              0,
              0.3
            )
            this.overlayTargets[index].style.opacity = overlayOpacity
          }
        }
      }
    })
  }
}
