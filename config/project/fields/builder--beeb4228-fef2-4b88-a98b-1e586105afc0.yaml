columnSuffix: null
handle: builder
instructions: null
name: Builder
searchable: false
settings:
  createButtonLabel: null
  defaultIndexViewMode: cards
  enableVersioning: false
  entryTypes:
    -
      __assoc__:
        -
          - uid
          - e1a2d0da-562c-4eb0-950a-4eeee63aa686 # Enquiries
    -
      __assoc__:
        -
          - uid
          - 571bde1a-6534-4ec3-b84f-7e429970ba61 # Hero
    -
      __assoc__:
        -
          - uid
          - 8b6b6d71-48c5-4bce-a415-5cac983497b6 # Image & text
    -
      __assoc__:
        -
          - uid
          - 4c80385b-3e2b-4999-ab9a-d67646fea278 # Info
    -
      __assoc__:
        -
          - uid
          - 492881c6-a939-44f5-955a-8e95df3d8c39 # Petals
    -
      __assoc__:
        -
          - uid
          - deeb1171-4fae-4cc4-981d-6b4850399ccd # Process
    -
      __assoc__:
        -
          - uid
          - cd9347c5-b986-415e-82ca-9ff329802a0c # Products
    -
      __assoc__:
        -
          - uid
          - f5316e0b-c8e8-4ea5-8218-92ac32dccbb5 # Rich Text
    -
      __assoc__:
        -
          - uid
          - 43066f50-e4af-4855-8061-8576b4e58dbe # Slider
  includeTableView: false
  maxEntries: null
  minEntries: null
  pageSize: null
  propagationKeyFormat: null
  propagationMethod: all
  showCardsInGrid: false
  viewMode: blocks
translationKeyFormat: null
translationMethod: site
type: craft\fields\Matrix
