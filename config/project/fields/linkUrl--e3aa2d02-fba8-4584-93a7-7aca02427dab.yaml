columnSuffix: null
handle: linkUrl
instructions: null
name: Link
searchable: false
settings:
  advancedFields:
    - target
  fullGraphqlData: true
  maxLength: 255
  showLabelField: false
  typeSettings:
    __assoc__:
      -
        - asset
        -
          __assoc__:
            -
              - sources
              - '*'
            -
              - allowedKinds
              - '*'
            -
              - showUnpermittedVolumes
              - ''
            -
              - showUnpermittedFiles
              - ''
      -
        - entry
        -
          __assoc__:
            -
              - sources
              - '*'
            -
              - showUnpermittedSections
              - ''
            -
              - showUnpermittedEntries
              - ''
      -
        - url
        -
          __assoc__:
            -
              - allowRootRelativeUrls
              - '1'
            -
              - allowAnchors
              - '1'
            -
              - allowCustomSchemes
              - ''
  types:
    - entry
    - url
    - asset
    - email
    - tel
    - sms
translationKeyFormat: null
translationMethod: none
type: craft\fields\Link
