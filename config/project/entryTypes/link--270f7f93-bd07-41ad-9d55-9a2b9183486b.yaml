color: null
fieldLayouts:
  ec5b7a3a-ccfd-4159-9dae-9e6a332204aa:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-05-21T08:55:26+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: Label
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: d36949be-4eb0-42c9-b72a-************
            userCondition: null
            warning: null
            width: 50
          -
            dateAdded: '2025-05-21T08:56:36+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: e3aa2d02-fba8-4584-93a7-7aca02427dab # Link
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: f8314313-fc7a-4245-8999-5479927371af
            userCondition: null
            warning: null
            width: 50
        name: Content
        uid: a509382f-3c03-4fda-b9e3-915cdfce3847
        userCondition: null
handle: link
hasTitleField: true
icon: null
name: Link
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
