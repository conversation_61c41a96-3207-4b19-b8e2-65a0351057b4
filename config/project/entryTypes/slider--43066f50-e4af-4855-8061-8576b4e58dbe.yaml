color: null
fieldLayouts:
  573eec6d-c6ec-4ad2-bacb-d4c956fba974:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-05-23T08:21:01+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 2f786f68-3ee9-460f-8f32-52e4c09be2f3
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-23T08:22:20+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 5317a8ad-92d0-485b-9120-5ddf71ad0b42 # Rich text
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d8f86e41-8382-4e8c-af55-60951632e60d
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-23T08:22:20+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 22dfbdba-6760-4ef7-af24-a8f86270cc80 # Images
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 7f8d1ffe-eaac-4b87-97d7-b78ad6672961
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 9573b213-d54f-4278-83e8-67f093f596a9
        userCondition: null
handle: slider
hasTitleField: true
icon: null
name: Slider
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
