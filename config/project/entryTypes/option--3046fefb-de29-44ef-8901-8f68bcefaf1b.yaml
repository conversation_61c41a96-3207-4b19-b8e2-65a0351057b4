color: null
fieldLayouts:
  c4e42858-e080-4ead-aab8-7a49edc90028:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-05-21T11:41:07+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: Label
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: c35cb327-6b8f-4faa-acee-53634f42c698
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-21T11:43:27+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 5317a8ad-92d0-485b-9120-5ddf71ad0b42 # Rich text
            handle: text
            includeInCards: false
            instructions: null
            label: Text
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: be69d90f-c490-4917-b0db-560dd7d3d93b
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: e069c740-26e4-4128-a10f-132233848dee
        userCondition: null
handle: option
hasTitleField: true
icon: null
name: Option
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
