color: null
fieldLayouts:
  1ef69f58-62d5-4b51-a2da-64fdf16a4915:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-05-26T10:43:25+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 0e809647-db56-4adb-9125-b54788d3e685
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-26T10:44:51+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 5317a8ad-92d0-485b-9120-5ddf71ad0b42 # Rich text
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: f35eb92b-d731-4699-bd6e-c557dfdd5f93
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-26T10:44:51+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: e59e875b-99f8-4586-8ec4-9ae6e204ba49 # Image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 317d1c79-e599-4e16-a56d-2bc99c3572dc
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: a328f2b0-e950-4212-b95e-f621f9a405ee
        userCondition: null
handle: hero
hasTitleField: true
icon: null
name: Hero
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
