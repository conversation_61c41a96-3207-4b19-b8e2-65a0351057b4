color: null
fieldLayouts:
  abaa5820-3519-4a5a-8a55-122c3e17876d:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-05-27T08:14:45+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: b6b0f63c-1361-4df6-bc12-7526a1dafafa
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-27T08:16:04+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 5317a8ad-92d0-485b-9120-5ddf71ad0b42 # Rich text
            handle: intro
            includeInCards: false
            instructions: null
            label: Intro
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 23a0b277-e6c4-4860-a348-acd20f756a1a
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: bfe55700-c10e-4f76-8559-e253602fa162
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-05-27T08:21:04+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 5317a8ad-92d0-485b-9120-5ddf71ad0b42 # Rich text
            handle: smallOrdersText
            includeInCards: false
            instructions: null
            label: 'Small orders text'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 4e036360-c63e-4983-8256-fc308234e7f9
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-27T08:21:04+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 9b0ec7e1-aed4-4e30-8c1d-57e11b1d36cf # Offices
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 85cae22a-6cdb-4fc4-ac03-fcf82edd45d7
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-27T08:21:04+00:00'
            elementCondition: null
            type: craft\fieldlayoutelements\HorizontalRule
            uid: 9729c184-130e-4c16-96ea-0b65bea03137
            userCondition: null
          -
            dateAdded: '2025-05-27T08:21:04+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 5317a8ad-92d0-485b-9120-5ddf71ad0b42 # Rich text
            handle: bulkOrdersText
            includeInCards: false
            instructions: null
            label: 'Bulk orders text'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: cb52f56d-2664-44af-a753-6aec50eb9f9d
            userCondition: null
            warning: null
            width: 100
        name: Orders
        uid: cccfe3bd-73d5-4a68-9de5-6fcae1adf26e
        userCondition: null
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-05-27T08:32:56+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 925a2d0a-aefd-4511-bb80-5273d2570a7a # FAQ
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ed3fd27c-d07a-4522-8c3c-d884b1941f7e
            userCondition: null
            warning: null
            width: 100
        name: FAQ
        uid: 7ef9c433-5591-4941-b1a2-f28af707c36b
        userCondition: null
handle: enquiries
hasTitleField: true
icon: null
name: Enquiries
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
