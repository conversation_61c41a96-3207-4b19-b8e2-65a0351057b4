color: null
fieldLayouts:
  54cea6ec-19b3-4187-b451-4aad7375f02b:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-05-21T14:01:12+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: e59e875b-99f8-4586-8ec4-9ae6e204ba49 # Image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 785ebacf-1239-4f73-9a8b-9070c46ccbd7
            userCondition: null
            warning: null
            width: 100
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-05-21T13:59:16+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: d6ee648f-90aa-40e8-8420-8ac7ec91e4a1
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-21T14:01:12+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 5317a8ad-92d0-485b-9120-5ddf71ad0b42 # Rich text
            handle: description
            includeInCards: false
            instructions: null
            label: Description
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5aeeb776-abff-4bb3-848c-11833d8133bf
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-05-21T14:01:12+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 5317a8ad-92d0-485b-9120-5ddf71ad0b42 # Rich text
            handle: footer
            includeInCards: false
            instructions: null
            label: Footer
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e15ccd8f-14e0-4d1b-a4f9-ac6eaa6b36ab
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: fadd9114-409a-4414-8383-13455bbc5215
        userCondition: null
handle: step
hasTitleField: true
icon: null
name: Step
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
