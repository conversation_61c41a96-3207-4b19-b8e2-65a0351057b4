import { resolve } from 'path'
import { defineConfig } from 'vite'

export default defineConfig({
  base: '/dist/',
  server: {
    host: '0.0.0.0',
    strictPort: true,
  },
  build: {
    manifest: true,
    sourcemap: true,
    outDir: resolve(__dirname, 'web/dist'),
    emptyOutDir: true,
    rollupOptions: {
      input: {
        app: resolve(__dirname, 'src/js/app.js'),
      },
    },
  },
})
