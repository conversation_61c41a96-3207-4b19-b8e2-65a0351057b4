image: docker:latest

variables:
  APP_IMAGE: $CI_R<PERSON><PERSON>TRY_IMAGE/app:$CI_COMMIT_SHA
  FRONTEND_IMAGE: $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA
  APP_IMAGE_STAGING: $CI_REGISTRY_IMAGE/app:staging
  FRONTEND_IMAGE_STAGING: $CI_REGISTRY_IMAGE/frontend:staging
  APP_IMAGE_LATEST: $CI_REGISTRY_IMAGE/app:latest
  FRONTEND_IMAGE_LATEST: $CI_REGISTRY_IMAGE/frontend:latest

stages:
  - build
  - publish
  - deploy

before_script:
  - command -v docker >/dev/null 2>&1 && docker info
  - command -v docker >/dev/null 2>&1 && docker login -u gitlab-ci-token -p $CI_JOB_TOKEN vagrant.builtbybuffalo.com

build_app:
  stage: build
  only:
    - master
    - develop
  script:
    - docker build --pull -t $APP_IMAGE -f ./docker/php/Dockerfile .
    - docker push $APP_IMAGE

build_frontend:
  stage: build
  only:
    - master
    - develop
  script:
    - docker build --pull --target prod -t $FRONTEND_IMAGE -f ./docker/frontend/Dockerfile .
    - docker push $FRONTEND_IMAGE

publish_app_staging:
  stage: publish
  script:
    - docker pull $APP_IMAGE
    - docker tag $APP_IMAGE $APP_IMAGE_STAGING
    - docker push $APP_IMAGE_STAGING
  only:
    - develop

publish_frontend_staging:
  stage: publish
  script:
    - docker pull $FRONTEND_IMAGE
    - docker tag $FRONTEND_IMAGE $FRONTEND_IMAGE_STAGING
    - docker push $FRONTEND_IMAGE_STAGING
  only:
    - develop

publish_app_production:
  stage: publish
  script:
    - docker pull $APP_IMAGE
    - docker tag $APP_IMAGE $APP_IMAGE_LATEST
    - docker push $APP_IMAGE_LATEST
  only:
    - master

publish_frontend_production:
  stage: publish
  script:
    - docker pull $FRONTEND_IMAGE
    - docker tag $FRONTEND_IMAGE $FRONTEND_IMAGE_LATEST
    - docker push $FRONTEND_IMAGE_LATEST
  only:
    - master

deploy_staging:
  stage: deploy
  variables:
    VIRTUAL_HOST_NAME: botanical.buffalo03.co.uk
    IMAGE_TAG: staging
  environment:
    name: staging
    deployment_tier: staging
  before_script:
    - apk --update add openssh-client
    - mkdir ~/.ssh
    - mv $DOCKER_SSH_KNOWN_HOSTS ~/.ssh/known_hosts
    - chmod 600 $DOCKER_SSH_PRIVATE_KEY
    - eval $(ssh-agent -s)
    - ssh-add -k $DOCKER_SSH_PRIVATE_KEY
    - docker info
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN vagrant.builtbybuffalo.com
  script:
    - docker compose -f ./docker-staging.yml pull
    - docker compose -f ./docker-staging.yml run --rm -T console ./craft migrate/all --no-backup --interactive=0
    - docker compose -f ./docker-staging.yml run --rm -T console ./craft project-config/apply
    - docker compose -f ./docker-staging.yml run --rm -T frontend pnpm build
    - docker compose -f ./docker-staging.yml up -d
    - sleep 5
    - docker compose -f ./docker-staging.yml exec -T console php craft cache/flush-all
    - docker compose -f ./docker-staging.yml exec -T console php craft clear-caches/all
  only:
    - develop

.deploy_production:
  stage: deploy
  variables:
    VIRTUAL_HOST_NAME: botanical-confetti.com
    IMAGE_TAG: latest
  environment:
    name: production
    deployment_tier: production
  before_script:
    - apk --update add openssh-client
    - mkdir ~/.ssh
    - mv $DOCKER_SSH_KNOWN_HOSTS ~/.ssh/known_hosts
    - chmod 600 $DOCKER_SSH_PRIVATE_KEY
    - eval $(ssh-agent -s)
    - ssh-add -k $DOCKER_SSH_PRIVATE_KEY
    - docker info
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN vagrant.builtbybuffalo.com
  script:
    - docker compose -f ./docker-production.yml pull
    - docker compose -f ./docker-production.yml run --rm -T console ./craft migrate/all --no-backup --interactive=0
    - docker compose -f ./docker-production.yml run --rm -T console ./craft project-config/apply
    - docker compose -f ./docker-production.yml run --rm -T frontend pnpm build
    - docker compose -f ./docker-production.yml up -d
    - docker compose -f ./docker-production.yml exec -T console php craft cache/flush-all
    - docker compose -f ./docker-production.yml exec -T console php craft clear-caches/all
  only:
    - master
