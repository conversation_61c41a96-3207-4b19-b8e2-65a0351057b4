{"private": true, "type": "module", "scripts": {"dev": "vite -c vite.config.dev.js", "build": "vite build", "preview": "vite preview", "format": "prettier 'src/**/*.{css,js}' --write"}, "prettier": {"printWidth": 100, "semi": false, "singleQuote": true, "trailingComma": "all"}, "devDependencies": {"prettier": "^3.5.3", "vite-plugin-restart": "^0.4.2", "vite-plugin-stimulus-hmr": "^3.0.0"}, "dependencies": {"@csstools/postcss-global-data": "^2.1.1", "@hotwired/stimulus": "^3.2.2", "destyle.css": "^4.0.1", "postcss": "^8.5.3", "postcss-custom-media": "^10.0.8", "postcss-import": "^16.1.0", "postcss-import-ext-glob": "^2.1.1", "postcss-mixins": "^10.0.1", "postcss-nested": "^6.2.0", "postcss-preset-env": "^9.6.0", "postcss-utopia": "^1.1.0", "stimulus-controller-resolver": "^3.2.0", "swiper": "^11.2.8", "vite": "^5.4.19"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}