FROM ghcr.io/craftcms/image:8.2

WORKDIR /app

USER root
ADD https://github.com/composer/composer/releases/download/2.3.5/composer.phar /usr/local/bin/composer
RUN chmod +rx /usr/local/bin/composer
EXPOSE 9000

COPY ./docker/php/nginx.conf /etc/nginx/nginx.conf
COPY ./docker/php/https.conf /etc/nginx/https.conf
COPY ./docker/php/rewrites.conf /etc/nginx/rewrites.conf
COPY ./docker/php/supervisor.conf /etc/supervisord.d/nginx.ini
RUN mkdir -p /var/log/nginx && chown -R appuser:appgroup /var/log/nginx
RUN chown -R appuser:appgroup /var/lib/nginx && touch /run/nginx.pid && chown -R appuser:appgroup /run/nginx.pid

USER appuser

COPY --chown=appuser:appgroup ./composer.json ./composer.lock ./
RUN composer install --ignore-platform-reqs --no-interaction --prefer-dist

COPY --chown=appuser:appgroup . /app
RUN mkdir -p /app/storage/runtime /app/storage/logs
RUN chown -R appuser:appgroup /app/storage /app/web/cpresources
RUN chmod -R 777 /app/storage/runtime /app/storage/logs
