worker_processes auto;
pid /run/nginx.pid;
daemon off;

events {
    worker_connections 1024;
}

http {
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 4096;
    server_tokens off;

    include /etc/nginx/mime.types;

    access_log /dev/stdout;
    error_log /dev/stderr;

    large_client_header_buffers 4 8k;

    gzip on;
    gzip_comp_level 5;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
    gzip_types
        application/atom+xml
        application/javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rss+xml
        application/vnd.geo+json
        application/vnd.ms-fontobject
        application/x-font-ttf
        application/x-web-app-manifest+json
        application/xhtml+xml
        application/xml
        font/opentype
        image/bmp
        image/svg+xml
        image/x-icon
        text/cache-manifest
        text/css
        text/plain
        text/vcard
        text/vnd.rim.location.xloc
        text/vtt
        text/x-component
        text/x-cross-domain-policy;

    log_format vhost '$host $remote_addr - $remote_user [$time_local] '
        '"$request" $status $body_bytes_sent '
        '"$http_referer" "$http_user_agent"';

    map $sent_http_content_type $expires {
        default                  off;
        text/html                epoch;
        text/css                 max;
        application/javascript   max;
        ~image/                  max;
        ~font/                   max;
    }

    server {
        expires $expires;

        include rewrites.conf;

        listen 8080;
        listen [::]:8080;
        access_log /dev/stdout;
        error_log /dev/stderr;
        root /app/web;
        index index.html index.htm index.php;
        server_name _;
        charset utf-8;

        location /ping {
            access_log off;
            fastcgi_read_timeout 5s;
            include        fastcgi_params;
            fastcgi_param  SCRIPT_NAME     /ping;
            fastcgi_param  SCRIPT_FILENAME /ping;
            fastcgi_pass   unix:/var/run/php/php-fpm.sock;
        }

        location / {
            try_files $uri $uri/ /index.php?$query_string;
        }

        location ~ \.php$ {
            try_files $uri =404;
            fastcgi_hide_header X-Powered-By;
            fastcgi_split_path_info ^(.+\.php)(/.+)$;
            fastcgi_pass unix:/var/run/php/php-fpm.sock;
            fastcgi_index index.php;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param PATH_INFO $fastcgi_path_info;
            include https.conf;
        }
    }
}
